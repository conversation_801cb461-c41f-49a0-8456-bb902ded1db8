import gradio as gr
from ragflow_retrieval_gradio import get_raw_chunks

import os
# Proxy settings
os.environ["DDGS_PROXY"] = "http://*************:80"
os.environ["http_proxy"] = "http://*************:80"
os.environ["https_proxy"] = "http://*************:443"
os.environ["no_proxy"] = "eoaagmld003,eoaagmld007,ai.cgg.com,127.0.0.1,localhost,eoaagmld007.int.cgg.com"

os.environ["REQUESTS_CA_BUNDLE"] = "/etc/ssl/certs/ca-certificates.crt"
os.environ["CURL_CA_BUNDLE"] = "/etc/ssl/certs/ca-certificates.crt"
os.environ["SSL_CERT_FILE"] = "/etc/ssl/certs/ca-certificates.crt"

def run_query(query, chat_id="********************************"):
    """Wrapper to call get_raw_chunks and return structured result."""
    chunks = get_raw_chunks(query, chat_id)
    # You can adjust how you display or format the chunks here
    return chunks

iface = gr.Interface(
    fn=run_query,
    inputs=[
        gr.Textbox(label="Question", lines=2, placeholder="Enter your query here..."),
        gr.Textbox(label="Chat ID (optional)", value="********************************")
    ],
    outputs=gr.JSON(label="Retrieved Chunks"),
    title="RAG Flow Retrieval Interface",
    description="Enter a question to retrieve relevant document chunks from the RAG system."
)

if __name__ == "__main__":
    # Bind to specific hostname and default HTTP port
    iface.launch(server_name="eoaagmld007.int.cgg.com", server_port=7860, share=False)
