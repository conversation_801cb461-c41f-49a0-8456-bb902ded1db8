2025-07-03 23:12:25,209 INFO     34 task_executor_549bc37d4deb_0 log path: /ragflow/logs/task_executor_549bc37d4deb_0.log, log levels: {'peewee': 'WARNING', 'pdfminer': 'WARNING', 'root': 'INFO'}
2025-07-03 23:12:25,210 INFO     34 
  ______           __      ______                     __
 /_  __/___ ______/ /__   / ____/  _____  _______  __/ /_____  _____
  / / / __ `/ ___/ //_/  / __/ | |/_/ _ \/ ___/ / / / __/ __ \/ ___/
 / / / /_/ (__  ) ,<    / /____>  </  __/ /__/ /_/ / /_/ /_/ / /
/_/  \__,_/____/_/|_|  /_____/_/|_|\___/\___/\__,_/\__/\____/_/
    
2025-07-03 23:12:25,211 INFO     34 TaskExecutor: RAGFlow version: v0.19.0 full
2025-07-03 23:12:25,213 INFO     34 Use Elasticsearch http://es01:9200 as the doc engine.
2025-07-03 23:12:25,229 INFO     34 GET http://es01:9200/ [status:200 duration:0.014s]
2025-07-03 23:12:25,236 INFO     34 HEAD http://es01:9200/ [status:200 duration:0.006s]
2025-07-03 23:12:25,236 INFO     34 Elasticsearch http://es01:9200 is healthy.
2025-07-03 23:12:25,247 WARNING  34 Load term.freq FAIL!
2025-07-03 23:12:25,253 WARNING  34 Realtime synonym is disabled, since no redis connection.
2025-07-03 23:12:25,260 WARNING  34 Load term.freq FAIL!
2025-07-03 23:12:25,264 WARNING  34 Realtime synonym is disabled, since no redis connection.
2025-07-03 23:12:25,264 INFO     34 MAX_CONTENT_LENGTH: 134217728
2025-07-03 23:12:25,266 INFO     34 MAX_FILE_COUNT_PER_USER: 0
2025-07-03 23:12:25,311 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-07-03 23:12:25,312 INFO     34 task_executor_549bc37d4deb_0 reported heartbeat: {"name": "task_executor_549bc37d4deb_0", "now": "2025-07-03T23:12:25.310+08:00", "boot_at": "2025-07-03T23:12:25.207+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-07-03 23:12:25,316 WARNING  34 RedisDB.get_unacked_iterator queue rag_flow_svr_queue_1 doesn't exist
2025-07-03 23:12:25,317 WARNING  34 RedisDB.get_unacked_iterator queue rag_flow_svr_queue doesn't exist
2025-07-03 23:12:55,317 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-07-03 23:12:55,320 INFO     34 task_executor_549bc37d4deb_0 reported heartbeat: {"name": "task_executor_549bc37d4deb_0", "now": "2025-07-03T23:12:55.316+08:00", "boot_at": "2025-07-03T23:12:25.207+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-07-03 23:13:25,328 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-07-03 23:13:25,330 INFO     34 task_executor_549bc37d4deb_0 reported heartbeat: {"name": "task_executor_549bc37d4deb_0", "now": "2025-07-03T23:13:25.327+08:00", "boot_at": "2025-07-03T23:12:25.207+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-07-03 23:13:55,338 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-07-03 23:13:55,342 INFO     34 task_executor_549bc37d4deb_0 reported heartbeat: {"name": "task_executor_549bc37d4deb_0", "now": "2025-07-03T23:13:55.337+08:00", "boot_at": "2025-07-03T23:12:25.207+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-07-03 23:14:25,349 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-07-03 23:14:25,352 INFO     34 task_executor_549bc37d4deb_0 reported heartbeat: {"name": "task_executor_549bc37d4deb_0", "now": "2025-07-03T23:14:25.349+08:00", "boot_at": "2025-07-03T23:12:25.207+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-07-03 23:14:55,359 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-07-03 23:14:55,360 INFO     34 task_executor_549bc37d4deb_0 reported heartbeat: {"name": "task_executor_549bc37d4deb_0", "now": "2025-07-03T23:14:55.358+08:00", "boot_at": "2025-07-03T23:12:25.207+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-07-03 23:15:25,367 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-07-03 23:15:25,369 INFO     34 task_executor_549bc37d4deb_0 reported heartbeat: {"name": "task_executor_549bc37d4deb_0", "now": "2025-07-03T23:15:25.366+08:00", "boot_at": "2025-07-03T23:12:25.207+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-07-03 23:15:55,380 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-07-03 23:15:55,381 INFO     34 task_executor_549bc37d4deb_0 reported heartbeat: {"name": "task_executor_549bc37d4deb_0", "now": "2025-07-03T23:15:55.379+08:00", "boot_at": "2025-07-03T23:12:25.207+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-07-03 23:16:00,443 ERROR    34 RedisDB.queue_consumer rag_flow_svr_queue_1 got exception: Error 111 connecting to redis:6379. Connection refused.
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 193, in collect
    redis_msg = next(UNACKED_ITERATOR)
StopIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 1116, in get_connection
    if connection.can_read() and connection.client_cache is None:
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 524, in can_read
    return self._parser.can_read(timeout)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/_parsers/base.py", line 128, in can_read
    return self._buffer and self._buffer.can_read(timeout)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/_parsers/socket.py", line 95, in can_read
    return bool(self.unread_bytes()) or self._read_from_socket(
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/_parsers/socket.py", line 68, in _read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
valkey.exceptions.ConnectionError: Connection closed by server.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 302, in connect
    sock = self.retry.call_with_retry(
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/retry.py", line 62, in call_with_retry
    return do()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 303, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 728, in _connect
    raise err
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 716, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/utils/redis_conn.py", line 228, in queue_consumer
    group_info = self.REDIS.xinfo_groups(queue_name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/commands/core.py", line 3810, in xinfo_groups
    return self.execute_command("XINFO GROUPS", name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/client.py", line 564, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 1120, in get_connection
    connection.connect()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 308, in connect
    raise ConnectionError(self._error_message(e))
valkey.exceptions.ConnectionError: Error 111 connecting to redis:6379. Connection refused.
2025-07-03 23:16:00,461 ERROR    34 RedisDB.queue_consumer rag_flow_svr_queue got exception: Error 111 connecting to redis:6379. Connection refused.
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 193, in collect
    redis_msg = next(UNACKED_ITERATOR)
StopIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 302, in connect
    sock = self.retry.call_with_retry(
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/retry.py", line 62, in call_with_retry
    return do()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 303, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 728, in _connect
    raise err
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 716, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/utils/redis_conn.py", line 228, in queue_consumer
    group_info = self.REDIS.xinfo_groups(queue_name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/commands/core.py", line 3810, in xinfo_groups
    return self.execute_command("XINFO GROUPS", name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/client.py", line 564, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 1107, in get_connection
    connection.connect()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 308, in connect
    raise ConnectionError(self._error_message(e))
valkey.exceptions.ConnectionError: Error 111 connecting to redis:6379. Connection refused.
2025-07-03 23:16:00,513 ERROR    34 RedisDB.queue_consumer rag_flow_svr_queue_1 got exception: Error 111 connecting to redis:6379. Connection refused.
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 193, in collect
    redis_msg = next(UNACKED_ITERATOR)
StopIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 302, in connect
    sock = self.retry.call_with_retry(
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/retry.py", line 62, in call_with_retry
    return do()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 303, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 728, in _connect
    raise err
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 716, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/utils/redis_conn.py", line 228, in queue_consumer
    group_info = self.REDIS.xinfo_groups(queue_name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/commands/core.py", line 3810, in xinfo_groups
    return self.execute_command("XINFO GROUPS", name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/client.py", line 564, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 1107, in get_connection
    connection.connect()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 308, in connect
    raise ConnectionError(self._error_message(e))
valkey.exceptions.ConnectionError: Error 111 connecting to redis:6379. Connection refused.
2025-07-03 23:16:00,520 ERROR    34 RedisDB.queue_consumer rag_flow_svr_queue got exception: Error 111 connecting to redis:6379. Connection refused.
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 193, in collect
    redis_msg = next(UNACKED_ITERATOR)
StopIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 302, in connect
    sock = self.retry.call_with_retry(
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/retry.py", line 62, in call_with_retry
    return do()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 303, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 728, in _connect
    raise err
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 716, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/utils/redis_conn.py", line 228, in queue_consumer
    group_info = self.REDIS.xinfo_groups(queue_name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/commands/core.py", line 3810, in xinfo_groups
    return self.execute_command("XINFO GROUPS", name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/client.py", line 564, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 1107, in get_connection
    connection.connect()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 308, in connect
    raise ConnectionError(self._error_message(e))
valkey.exceptions.ConnectionError: Error 111 connecting to redis:6379. Connection refused.
2025-07-03 23:16:00,617 ERROR    34 RedisDB.queue_consumer rag_flow_svr_queue_1 got exception: Error 111 connecting to redis:6379. Connection refused.
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 193, in collect
    redis_msg = next(UNACKED_ITERATOR)
StopIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 302, in connect
    sock = self.retry.call_with_retry(
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/retry.py", line 62, in call_with_retry
    return do()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 303, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 728, in _connect
    raise err
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 716, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/utils/redis_conn.py", line 228, in queue_consumer
    group_info = self.REDIS.xinfo_groups(queue_name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/commands/core.py", line 3810, in xinfo_groups
    return self.execute_command("XINFO GROUPS", name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/client.py", line 564, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 1107, in get_connection
    connection.connect()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 308, in connect
    raise ConnectionError(self._error_message(e))
valkey.exceptions.ConnectionError: Error 111 connecting to redis:6379. Connection refused.
2025-07-03 23:16:00,624 ERROR    34 RedisDB.queue_consumer rag_flow_svr_queue got exception: Error 111 connecting to redis:6379. Connection refused.
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 193, in collect
    redis_msg = next(UNACKED_ITERATOR)
StopIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 302, in connect
    sock = self.retry.call_with_retry(
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/retry.py", line 62, in call_with_retry
    return do()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 303, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 728, in _connect
    raise err
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 716, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/utils/redis_conn.py", line 228, in queue_consumer
    group_info = self.REDIS.xinfo_groups(queue_name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/commands/core.py", line 3810, in xinfo_groups
    return self.execute_command("XINFO GROUPS", name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/client.py", line 564, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 1107, in get_connection
    connection.connect()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 308, in connect
    raise ConnectionError(self._error_message(e))
valkey.exceptions.ConnectionError: Error 111 connecting to redis:6379. Connection refused.
2025-07-03 23:16:00,720 ERROR    34 RedisDB.queue_consumer rag_flow_svr_queue_1 got exception: Error 111 connecting to redis:6379. Connection refused.
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 193, in collect
    redis_msg = next(UNACKED_ITERATOR)
StopIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 302, in connect
    sock = self.retry.call_with_retry(
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/retry.py", line 62, in call_with_retry
    return do()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 303, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 728, in _connect
    raise err
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 716, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/utils/redis_conn.py", line 228, in queue_consumer
    group_info = self.REDIS.xinfo_groups(queue_name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/commands/core.py", line 3810, in xinfo_groups
    return self.execute_command("XINFO GROUPS", name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/client.py", line 564, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 1107, in get_connection
    connection.connect()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 308, in connect
    raise ConnectionError(self._error_message(e))
valkey.exceptions.ConnectionError: Error 111 connecting to redis:6379. Connection refused.
2025-07-03 23:16:00,726 ERROR    34 RedisDB.queue_consumer rag_flow_svr_queue got exception: Error 111 connecting to redis:6379. Connection refused.
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 193, in collect
    redis_msg = next(UNACKED_ITERATOR)
StopIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 302, in connect
    sock = self.retry.call_with_retry(
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/retry.py", line 62, in call_with_retry
    return do()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 303, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 728, in _connect
    raise err
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 716, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/utils/redis_conn.py", line 228, in queue_consumer
    group_info = self.REDIS.xinfo_groups(queue_name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/commands/core.py", line 3810, in xinfo_groups
    return self.execute_command("XINFO GROUPS", name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/client.py", line 564, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 1107, in get_connection
    connection.connect()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 308, in connect
    raise ConnectionError(self._error_message(e))
valkey.exceptions.ConnectionError: Error 111 connecting to redis:6379. Connection refused.
2025-07-03 23:16:00,821 ERROR    34 RedisDB.queue_consumer rag_flow_svr_queue_1 got exception: Error 111 connecting to redis:6379. Connection refused.
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 193, in collect
    redis_msg = next(UNACKED_ITERATOR)
StopIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 302, in connect
    sock = self.retry.call_with_retry(
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/retry.py", line 62, in call_with_retry
    return do()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 303, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 728, in _connect
    raise err
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 716, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/utils/redis_conn.py", line 228, in queue_consumer
    group_info = self.REDIS.xinfo_groups(queue_name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/commands/core.py", line 3810, in xinfo_groups
    return self.execute_command("XINFO GROUPS", name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/client.py", line 564, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 1107, in get_connection
    connection.connect()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 308, in connect
    raise ConnectionError(self._error_message(e))
valkey.exceptions.ConnectionError: Error 111 connecting to redis:6379. Connection refused.
2025-07-03 23:16:00,828 ERROR    34 RedisDB.queue_consumer rag_flow_svr_queue got exception: Error 111 connecting to redis:6379. Connection refused.
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 193, in collect
    redis_msg = next(UNACKED_ITERATOR)
StopIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 302, in connect
    sock = self.retry.call_with_retry(
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/retry.py", line 62, in call_with_retry
    return do()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 303, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 728, in _connect
    raise err
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 716, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/utils/redis_conn.py", line 228, in queue_consumer
    group_info = self.REDIS.xinfo_groups(queue_name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/commands/core.py", line 3810, in xinfo_groups
    return self.execute_command("XINFO GROUPS", name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/client.py", line 564, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 1107, in get_connection
    connection.connect()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 308, in connect
    raise ConnectionError(self._error_message(e))
valkey.exceptions.ConnectionError: Error 111 connecting to redis:6379. Connection refused.
2025-07-03 23:16:05,471 ERROR    34 RedisDB.queue_consumer rag_flow_svr_queue_1 got exception: Error 111 connecting to redis:6379. Connection refused.
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 193, in collect
    redis_msg = next(UNACKED_ITERATOR)
StopIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 302, in connect
    sock = self.retry.call_with_retry(
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/retry.py", line 62, in call_with_retry
    return do()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 303, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 728, in _connect
    raise err
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 716, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/utils/redis_conn.py", line 228, in queue_consumer
    group_info = self.REDIS.xinfo_groups(queue_name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/commands/core.py", line 3810, in xinfo_groups
    return self.execute_command("XINFO GROUPS", name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/client.py", line 564, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 1107, in get_connection
    connection.connect()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 308, in connect
    raise ConnectionError(self._error_message(e))
valkey.exceptions.ConnectionError: Error 111 connecting to redis:6379. Connection refused.
2025-07-03 23:16:05,477 ERROR    34 RedisDB.queue_consumer rag_flow_svr_queue got exception: Error 111 connecting to redis:6379. Connection refused.
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 193, in collect
    redis_msg = next(UNACKED_ITERATOR)
StopIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 302, in connect
    sock = self.retry.call_with_retry(
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/retry.py", line 62, in call_with_retry
    return do()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 303, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 728, in _connect
    raise err
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 716, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/utils/redis_conn.py", line 228, in queue_consumer
    group_info = self.REDIS.xinfo_groups(queue_name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/commands/core.py", line 3810, in xinfo_groups
    return self.execute_command("XINFO GROUPS", name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/client.py", line 564, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 1107, in get_connection
    connection.connect()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 308, in connect
    raise ConnectionError(self._error_message(e))
valkey.exceptions.ConnectionError: Error 111 connecting to redis:6379. Connection refused.
2025-07-03 23:16:05,527 ERROR    34 RedisDB.queue_consumer rag_flow_svr_queue_1 got exception: Error 111 connecting to redis:6379. Connection refused.
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 193, in collect
    redis_msg = next(UNACKED_ITERATOR)
StopIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 302, in connect
    sock = self.retry.call_with_retry(
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/retry.py", line 62, in call_with_retry
    return do()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 303, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 728, in _connect
    raise err
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 716, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/utils/redis_conn.py", line 228, in queue_consumer
    group_info = self.REDIS.xinfo_groups(queue_name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/commands/core.py", line 3810, in xinfo_groups
    return self.execute_command("XINFO GROUPS", name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/client.py", line 564, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 1107, in get_connection
    connection.connect()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 308, in connect
    raise ConnectionError(self._error_message(e))
valkey.exceptions.ConnectionError: Error 111 connecting to redis:6379. Connection refused.
2025-07-03 23:16:05,534 ERROR    34 RedisDB.queue_consumer rag_flow_svr_queue got exception: Error 111 connecting to redis:6379. Connection refused.
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 193, in collect
    redis_msg = next(UNACKED_ITERATOR)
StopIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 302, in connect
    sock = self.retry.call_with_retry(
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/retry.py", line 62, in call_with_retry
    return do()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 303, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 728, in _connect
    raise err
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 716, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/utils/redis_conn.py", line 228, in queue_consumer
    group_info = self.REDIS.xinfo_groups(queue_name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/commands/core.py", line 3810, in xinfo_groups
    return self.execute_command("XINFO GROUPS", name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/client.py", line 564, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 1107, in get_connection
    connection.connect()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 308, in connect
    raise ConnectionError(self._error_message(e))
valkey.exceptions.ConnectionError: Error 111 connecting to redis:6379. Connection refused.
2025-07-03 23:16:05,633 ERROR    34 RedisDB.queue_consumer rag_flow_svr_queue_1 got exception: Error 111 connecting to redis:6379. Connection refused.
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 193, in collect
    redis_msg = next(UNACKED_ITERATOR)
StopIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 302, in connect
    sock = self.retry.call_with_retry(
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/retry.py", line 62, in call_with_retry
    return do()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 303, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 728, in _connect
    raise err
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 716, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/utils/redis_conn.py", line 228, in queue_consumer
    group_info = self.REDIS.xinfo_groups(queue_name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/commands/core.py", line 3810, in xinfo_groups
    return self.execute_command("XINFO GROUPS", name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/client.py", line 564, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 1107, in get_connection
    connection.connect()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 308, in connect
    raise ConnectionError(self._error_message(e))
valkey.exceptions.ConnectionError: Error 111 connecting to redis:6379. Connection refused.
2025-07-03 23:16:05,639 ERROR    34 RedisDB.queue_consumer rag_flow_svr_queue got exception: Error 111 connecting to redis:6379. Connection refused.
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 193, in collect
    redis_msg = next(UNACKED_ITERATOR)
StopIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 302, in connect
    sock = self.retry.call_with_retry(
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/retry.py", line 62, in call_with_retry
    return do()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 303, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 728, in _connect
    raise err
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 716, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/utils/redis_conn.py", line 228, in queue_consumer
    group_info = self.REDIS.xinfo_groups(queue_name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/commands/core.py", line 3810, in xinfo_groups
    return self.execute_command("XINFO GROUPS", name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/client.py", line 564, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 1107, in get_connection
    connection.connect()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 308, in connect
    raise ConnectionError(self._error_message(e))
valkey.exceptions.ConnectionError: Error 111 connecting to redis:6379. Connection refused.
2025-07-03 23:16:05,734 ERROR    34 RedisDB.queue_consumer rag_flow_svr_queue_1 got exception: Error 111 connecting to redis:6379. Connection refused.
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 193, in collect
    redis_msg = next(UNACKED_ITERATOR)
StopIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 302, in connect
    sock = self.retry.call_with_retry(
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/retry.py", line 62, in call_with_retry
    return do()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 303, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 728, in _connect
    raise err
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 716, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/utils/redis_conn.py", line 228, in queue_consumer
    group_info = self.REDIS.xinfo_groups(queue_name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/commands/core.py", line 3810, in xinfo_groups
    return self.execute_command("XINFO GROUPS", name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/client.py", line 564, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 1107, in get_connection
    connection.connect()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 308, in connect
    raise ConnectionError(self._error_message(e))
valkey.exceptions.ConnectionError: Error 111 connecting to redis:6379. Connection refused.
2025-07-03 23:16:05,741 ERROR    34 RedisDB.queue_consumer rag_flow_svr_queue got exception: Error 111 connecting to redis:6379. Connection refused.
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 193, in collect
    redis_msg = next(UNACKED_ITERATOR)
StopIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 302, in connect
    sock = self.retry.call_with_retry(
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/retry.py", line 62, in call_with_retry
    return do()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 303, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 728, in _connect
    raise err
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 716, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/utils/redis_conn.py", line 228, in queue_consumer
    group_info = self.REDIS.xinfo_groups(queue_name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/commands/core.py", line 3810, in xinfo_groups
    return self.execute_command("XINFO GROUPS", name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/client.py", line 564, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 1107, in get_connection
    connection.connect()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 308, in connect
    raise ConnectionError(self._error_message(e))
valkey.exceptions.ConnectionError: Error 111 connecting to redis:6379. Connection refused.
2025-07-03 23:16:05,836 ERROR    34 RedisDB.queue_consumer rag_flow_svr_queue_1 got exception: Error 111 connecting to redis:6379. Connection refused.
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 193, in collect
    redis_msg = next(UNACKED_ITERATOR)
StopIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 302, in connect
    sock = self.retry.call_with_retry(
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/retry.py", line 62, in call_with_retry
    return do()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 303, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 728, in _connect
    raise err
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 716, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/utils/redis_conn.py", line 228, in queue_consumer
    group_info = self.REDIS.xinfo_groups(queue_name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/commands/core.py", line 3810, in xinfo_groups
    return self.execute_command("XINFO GROUPS", name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/client.py", line 564, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 1107, in get_connection
    connection.connect()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 308, in connect
    raise ConnectionError(self._error_message(e))
valkey.exceptions.ConnectionError: Error 111 connecting to redis:6379. Connection refused.
2025-07-03 23:16:05,842 ERROR    34 RedisDB.queue_consumer rag_flow_svr_queue got exception: Error 111 connecting to redis:6379. Connection refused.
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 193, in collect
    redis_msg = next(UNACKED_ITERATOR)
StopIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 302, in connect
    sock = self.retry.call_with_retry(
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/retry.py", line 62, in call_with_retry
    return do()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 303, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 728, in _connect
    raise err
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 716, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/utils/redis_conn.py", line 228, in queue_consumer
    group_info = self.REDIS.xinfo_groups(queue_name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/commands/core.py", line 3810, in xinfo_groups
    return self.execute_command("XINFO GROUPS", name)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/client.py", line 564, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 1107, in get_connection
    connection.connect()
  File "/ragflow/.venv/lib/python3.10/site-packages/valkey/connection.py", line 308, in connect
    raise ConnectionError(self._error_message(e))
valkey.exceptions.ConnectionError: Error 111 connecting to redis:6379. Connection refused.
