2025-08-01 00:11:47,693 INFO     36 task_executor_332ded9aa4f2_0 log path: /ragflow/logs/task_executor_332ded9aa4f2_0.log, log levels: {'peewee': 'WARNING', 'pdfminer': 'WARNING', 'root': 'INFO'}
2025-08-01 00:11:47,694 INFO     36 
  ______           __      ______                     __
 /_  __/___ ______/ /__   / ____/  _____  _______  __/ /_____  _____
  / / / __ `/ ___/ //_/  / __/ | |/_/ _ \/ ___/ / / / __/ __ \/ ___/
 / / / /_/ (__  ) ,<    / /____>  </  __/ /__/ /_/ / /_/ /_/ / /
/_/  \__,_/____/_/|_|  /_____/_/|_|\___/\___/\__,_/\__/\____/_/
    
2025-08-01 00:11:47,698 INFO     36 TaskExecutor: RAGFlow version: v0.19.0 full
2025-08-01 00:11:47,701 INFO     36 Use Elasticsearch http://es01:9200 as the doc engine.
2025-08-01 00:11:47,708 INFO     36 GET http://es01:9200/ [status:200 duration:0.003s]
2025-08-01 00:11:47,709 INFO     36 HEAD http://es01:9200/ [status:200 duration:0.001s]
2025-08-01 00:11:47,712 INFO     36 Elasticsearch http://es01:9200 is healthy.
2025-08-01 00:11:47,724 WARNING  36 Load term.freq FAIL!
2025-08-01 00:11:47,729 WARNING  36 Realtime synonym is disabled, since no redis connection.
2025-08-01 00:11:47,735 WARNING  36 Load term.freq FAIL!
2025-08-01 00:11:47,739 WARNING  36 Realtime synonym is disabled, since no redis connection.
2025-08-01 00:11:47,740 INFO     36 MAX_CONTENT_LENGTH: 134217728
2025-08-01 00:11:47,742 INFO     36 MAX_FILE_COUNT_PER_USER: 0
2025-08-01 00:11:47,755 INFO     36 task_executor_332ded9aa4f2_0 reported heartbeat: {"name": "task_executor_332ded9aa4f2_0", "now": "2025-08-01T00:11:47.752+08:00", "boot_at": "2025-08-01T00:11:47.688+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-01 00:11:47,761 WARNING  36 RedisDB.get_unacked_iterator queue rag_flow_svr_queue_1 doesn't exist
2025-08-01 00:12:17,763 INFO     36 task_executor_332ded9aa4f2_0 reported heartbeat: {"name": "task_executor_332ded9aa4f2_0", "now": "2025-08-01T00:12:17.761+08:00", "boot_at": "2025-08-01T00:11:47.688+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-01 00:12:47,771 INFO     36 task_executor_332ded9aa4f2_0 reported heartbeat: {"name": "task_executor_332ded9aa4f2_0", "now": "2025-08-01T00:12:47.769+08:00", "boot_at": "2025-08-01T00:11:47.688+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-01 00:13:17,780 INFO     36 task_executor_332ded9aa4f2_0 reported heartbeat: {"name": "task_executor_332ded9aa4f2_0", "now": "2025-08-01T00:13:17.778+08:00", "boot_at": "2025-08-01T00:11:47.688+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-01 00:13:47,792 INFO     36 task_executor_332ded9aa4f2_0 reported heartbeat: {"name": "task_executor_332ded9aa4f2_0", "now": "2025-08-01T00:13:47.790+08:00", "boot_at": "2025-08-01T00:11:47.688+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-01 00:14:03,582 INFO     36 handle_task begin for task {"id": "5dc3b8426e2911f0848702420ae90a06", "doc_id": "5a9e36d86e2911f0aca702420ae90a06", "from_page": 0, "to_page": 2, "retry_count": 0, "kb_id": "1dde70d26e2911f08f4502420ae90a06", "parser_id": "paper", "parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Liheyu Zhang - Employment Reference Letter ********.pdf", "type": "pdf", "location": "Liheyu Zhang - Employment Reference Letter ********.pdf", "size": 160834, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1753978439448, "task_type": ""}
2025-08-01 00:14:03,853 INFO     36 HTTP Request: POST http://eoaagmld003:8111/v1/embeddings "HTTP/1.1 200 OK"
2025-08-01 00:14:03,870 INFO     36 HEAD http://es01:9200/ragflow_ebd47f2646b611f0821c02420ae90706 [status:200 duration:0.003s]
2025-08-01 00:14:03,903 INFO     36 handle_task begin for task {"id": "5e7a352c6e2911f0bdae02420ae90a06", "doc_id": "52be32ce6e2911f0863602420ae90a06", "from_page": 0, "to_page": 4, "retry_count": 0, "kb_id": "1dde70d26e2911f08f4502420ae90a06", "parser_id": "paper", "parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Bank Holiday Swap Program Guide.pdf", "type": "pdf", "location": "Bank Holiday Swap Program Guide.pdf", "size": 482309, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": *************, "task_type": ""}
2025-08-01 00:14:04,105 INFO     36 HTTP Request: POST http://eoaagmld003:8111/v1/embeddings "HTTP/1.1 200 OK"
2025-08-01 00:14:04,118 INFO     36 HEAD http://es01:9200/ragflow_ebd47f2646b611f0821c02420ae90706 [status:200 duration:0.003s]
2025-08-01 00:14:04,146 INFO     36 handle_task begin for task {"id": "5f24b84e6e2911f0983202420ae90a06", "doc_id": "4a88ed1a6e2911f0a36102420ae90a06", "from_page": 0, "to_page": 18, "retry_count": 0, "kb_id": "1dde70d26e2911f08f4502420ae90a06", "parser_id": "paper", "parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "2505.14059v1.pdf", "type": "pdf", "location": "2505.14059v1.pdf", "size": 20037471, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1753978441755, "task_type": ""}
2025-08-01 00:14:04,353 INFO     36 HTTP Request: POST http://eoaagmld003:8111/v1/embeddings "HTTP/1.1 200 OK"
2025-08-01 00:14:04,370 INFO     36 HEAD http://es01:9200/ragflow_ebd47f2646b611f0821c02420ae90706 [status:200 duration:0.003s]
2025-08-01 00:14:04,486 INFO     36 From minio(0.****************) Liheyu Zhang - Employment Reference Letter ********.pdf/Liheyu Zhang - Employment Reference Letter ********.pdf
2025-08-01 00:14:04,583 INFO     36 From minio(0.****************) 2505.14059v1.pdf/2505.14059v1.pdf
2025-08-01 00:14:04,585 INFO     36 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-08-01 00:14:04,585 INFO     36 From minio(0.****************) Bank Holiday Swap Program Guide.pdf/Bank Holiday Swap Program Guide.pdf
2025-08-01 00:14:04,603 INFO     36 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-08-01 00:14:05,007 INFO     36 load_model /ragflow/rag/res/deepdoc/layout.paper.onnx uses CPU
2025-08-01 00:14:05,070 INFO     36 load_model /ragflow/rag/res/deepdoc/tsr.onnx uses CPU
2025-08-01 00:14:05,071 DEBUG    36 [RAGFlowPdfParser] custom table transformer set: <deepdoc.parser.pdf_parser.CustomTableStructureRecognizer object at 0x7f1c889c5810>
2025-08-01 00:14:05,093 INFO     36 set_progress(5dc3b8426e2911f0848702420ae90a06), progress: None, progress_msg: 00:14:05 Page(1~3): OCR started
2025-08-01 00:14:05,642 INFO     36 __images__ dedupe_chars cost 0.5480658039450645s
2025-08-01 00:14:05,646 WARNING  36 Miss outlines
2025-08-01 00:14:05,997 INFO     36 __ocr detecting boxes of a image cost (0.3410002887248993s)
2025-08-01 00:14:06,017 INFO     36 __ocr sorting 2016 chars cost 0.020012609660625458s
2025-08-01 00:14:06,087 INFO     36 __ocr recognize 49 boxes cost 0.06973203271627426s
2025-08-01 00:14:06,227 INFO     36 __ocr detecting boxes of a image cost (0.13745463639497757s)
2025-08-01 00:14:06,232 INFO     36 __ocr sorting 564 chars cost 0.0052215903997421265s
2025-08-01 00:14:06,278 INFO     36 __ocr recognize 15 boxes cost 0.042914099991321564s
2025-08-01 00:14:06,279 INFO     36 __images__ 2 pages cost 0.6293320953845978s
2025-08-01 00:14:06,290 INFO     36 set_progress(5dc3b8426e2911f0848702420ae90a06), progress: None, progress_msg: 00:14:06 Page(1~3): OCR finished (1.20s)
2025-08-01 00:14:06,291 DEBUG    36 DiT API call with original image size: (1786, 2526)
2025-08-01 00:14:06,810 DEBUG    36 DiT API call successful for image size: (1786, 2526)
2025-08-01 00:14:06,810 DEBUG    36 DiT API response for page 0: boxes=16
2025-08-01 00:14:06,818 DEBUG    36 DiT raw API boxes for page 0 (first 2): [[231.04630859374998, 1296.0468445633703, 1568.6746276855467, 1364.829255040824], [211.93815795898436, 915.156694569069, 1203.9091452026366, 1254.5552829570415]]
2025-08-01 00:14:06,821 DEBUG    36 DiT raw API classes for page 0 (first 2): ['TEXT', 'FORMS']
2025-08-01 00:14:06,829 DEBUG    36 DiT raw API scores for page 0 (first 2): [0.998035728931427, 0.9975742697715759]
2025-08-01 00:14:06,834 INFO     36 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-08-01 00:14:06,853 INFO     36 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-08-01 00:14:07,789 DEBUG    36 Applied OCR adjustment to 16 boxes
2025-08-01 00:14:07,789 DEBUG    36 DiT processed 16 boxes for page 0, first 3: [{'type': 'text', 'x0': 210.0, 'top': 1295.0, 'x1': 1573.0, 'bottom': 1373.0, 'score': 0.998035728931427}, {'type': 'text', 'x0': 208.0, 'top': 915.156694569069, 'x1': 1203.9091452026366, 'bottom': 1260.0, 'score': 0.9975742697715759}, {'type': 'text', 'x0': 213.0, 'top': 1884.0, 'x1': 686.0, 'bottom': 1952.0, 'score': 0.9972789883613586}]
2025-08-01 00:14:07,794 DEBUG    36 DiT API call with original image size: (1786, 2526)
2025-08-01 00:14:08,225 DEBUG    36 DiT API call successful for image size: (1786, 2526)
2025-08-01 00:14:08,226 DEBUG    36 DiT API response for page 1: boxes=8
2025-08-01 00:14:08,229 DEBUG    36 DiT raw API boxes for page 1 (first 2): [[208.04720846176147, 580.4245862884926, 541.9791632080078, 646.5020536630159], [220.23420372009278, 681.2219879393235, 1500.0214373779297, 747.5941731986695]]
2025-08-01 00:14:08,232 DEBUG    36 DiT raw API classes for page 1 (first 2): ['LOGO', 'TEXT']
2025-08-01 00:14:08,238 DEBUG    36 DiT raw API scores for page 1 (first 2): [0.9621598720550537, 0.9599483013153076]
2025-08-01 00:14:08,766 DEBUG    36 Applied OCR adjustment to 8 boxes
2025-08-01 00:14:08,766 DEBUG    36 DiT processed 8 boxes for page 1, first 3: [{'type': 'figure', 'x0': 208.04720846176147, 'top': 580.4245862884926, 'x1': 541.9791632080078, 'bottom': 646.5020536630159, 'score': 0.9621598720550537}, {'type': 'text', 'x0': 213.0, 'top': 679.0, 'x1': 1500.0214373779297, 'bottom': 750.0, 'score': 0.9599483013153076}, {'type': 'text', 'x0': 213.0, 'top': 518.0, 'x1': 678.0, 'bottom': 555.0, 'score': 0.9593035578727722}]
2025-08-01 00:14:08,772 DEBUG    36 DiT original boxes for page 0 (before scale_factor=3 division): [{'type': 'text', 'x0': 210.0, 'top': 1295.0, 'x1': 1573.0, 'bottom': 1373.0, 'score': 0.998035728931427}, {'type': 'text', 'x0': 208.0, 'top': 915.156694569069, 'x1': 1203.9091452026366, 'bottom': 1260.0, 'score': 0.9975742697715759}]
2025-08-01 00:14:08,775 DEBUG    36 DiT scaled boxes for page 0 (after scale_factor=3 division): [{'type': 'text', 'score': 0.998035728931427, 'x0': 70.0, 'x1': 524.3333333333334, 'top': 431.6666666666667, 'bottom': 457.6666666666667, 'page_number': 0}, {'type': 'text', 'score': 0.9975742697715759, 'x0': 69.33333333333333, 'x1': 401.3030484008789, 'top': 305.052231523023, 'bottom': 420.0, 'page_number': 0}]
2025-08-01 00:14:08,779 DEBUG    36 Image 0 dimensions: (1786, 2526)
2025-08-01 00:14:08,785 DEBUG    36 OCR boxes sample for page 0: [{'x0': 83.33333333333333, 'x1': 235.66666666666666, 'top': 67.66666666666667, 'text': 'VIRIDIEN', 'bottom': 86.0, 'page_number': 1}, {'x0': 468.6666666666667, 'x1': 524.3333333333334, 'top': 144.66666666666666, 'text': '2 April 2025 ', 'bottom': 157.0, 'page_number': 1}]
2025-08-01 00:14:08,794 DEBUG    36 DiT original boxes for page 1 (before scale_factor=3 division): [{'type': 'figure', 'x0': 208.04720846176147, 'top': 580.4245862884926, 'x1': 541.9791632080078, 'bottom': 646.5020536630159, 'score': 0.9621598720550537}, {'type': 'text', 'x0': 213.0, 'top': 679.0, 'x1': 1500.0214373779297, 'bottom': 750.0, 'score': 0.9599483013153076}]
2025-08-01 00:14:08,795 DEBUG    36 DiT scaled boxes for page 1 (after scale_factor=3 division): [{'type': 'figure', 'score': 0.9621598720550537, 'x0': 69.34906948725383, 'x1': 180.65972106933592, 'top': 193.4748620961642, 'bottom': 215.50068455433862, 'page_number': 1}, {'type': 'text', 'score': 0.9599483013153076, 'x0': 71.0, 'x1': 500.00714579264326, 'top': 226.33333333333334, 'bottom': 250.0, 'page_number': 1}]
2025-08-01 00:14:08,798 DEBUG    36 Image 1 dimensions: (1786, 2526)
2025-08-01 00:14:08,801 DEBUG    36 OCR boxes sample for page 1: [{'x0': 86.0, 'x1': 235.33333333333334, 'top': 66.66666666666667, 'text': 'VIRIDIEN', 'bottom': 87.66666666666667, 'page_number': 2}, {'x0': 70.33333333333333, 'x1': 153.33333333333334, 'top': 155.0, 'text': 'viridiengroup.com ', 'bottom': 167.33333333333334, 'page_number': 2}]
2025-08-01 00:14:08,831 INFO     36 set_progress(5dc3b8426e2911f0848702420ae90a06), progress: 0.63, progress_msg: 00:14:08 Page(1~3): Layout analysis (2.52s)
2025-08-01 00:14:08,854 INFO     36 set_progress(5dc3b8426e2911f0848702420ae90a06), progress: 0.68, progress_msg: 00:14:08 Page(1~3): Table analysis (0.00s)
2025-08-01 00:14:08,888 INFO     36 set_progress(5dc3b8426e2911f0848702420ae90a06), progress: 0.75, progress_msg: 00:14:08 Page(1~3): Text merged (0.01s)
2025-08-01 00:14:08,914 INFO     36 set_progress(5dc3b8426e2911f0848702420ae90a06), progress: 0.8, progress_msg: 00:14:08 Page(1~3): Page 0~2: Text merging finished
2025-08-01 00:14:09,006 INFO     36 Chunking(5.135562375187874) Liheyu Zhang - Employment Reference Letter ********.pdf/Liheyu Zhang - Employment Reference Letter ********.pdf done
2025-08-01 00:14:09,053 INFO     36 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-08-01 00:14:09,073 INFO     36 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-08-01 00:14:09,077 INFO     36 load_model /ragflow/rag/res/deepdoc/layout.paper.onnx reuses cached model
2025-08-01 00:14:09,087 INFO     36 load_model /ragflow/rag/res/deepdoc/tsr.onnx reuses cached model
2025-08-01 00:14:09,096 DEBUG    36 [RAGFlowPdfParser] custom table transformer set: <deepdoc.parser.pdf_parser.CustomTableStructureRecognizer object at 0x7f1bb8f40be0>
2025-08-01 00:14:09,112 INFO     36 MINIO PUT(Liheyu Zhang - Employment Reference Letter ********.pdf) cost 0.105 s
2025-08-01 00:14:09,113 INFO     36 Build document Liheyu Zhang - Employment Reference Letter ********.pdf: 5.24s
2025-08-01 00:14:09,147 INFO     36 set_progress(5dc3b8426e2911f0848702420ae90a06), progress: None, progress_msg: 00:14:09 Page(1~3): Generate 5 chunks
2025-08-01 00:14:09,159 INFO     36 set_progress(5f24b84e6e2911f0983202420ae90a06), progress: None, progress_msg: 00:14:09 Page(1~19): OCR started
2025-08-01 00:14:09,204 INFO     36 HTTP Request: POST http://eoaagmld003:8111/v1/embeddings "HTTP/1.1 200 OK"
2025-08-01 00:14:09,260 INFO     36 HTTP Request: POST http://eoaagmld003:8111/v1/embeddings "HTTP/1.1 200 OK"
2025-08-01 00:14:09,289 INFO     36 set_progress(5dc3b8426e2911f0848702420ae90a06), progress: 0.74, progress_msg: 
2025-08-01 00:14:09,289 INFO     36 Embedding chunks (0.14s)
2025-08-01 00:14:09,305 INFO     36 set_progress(5dc3b8426e2911f0848702420ae90a06), progress: None, progress_msg: 00:14:09 Page(1~3): Embedding chunks (0.14s)
2025-08-01 00:14:09,340 INFO     36 PUT http://es01:9200/ragflow_ebd47f2646b611f0821c02420ae90706/_bulk?refresh=false&timeout=60s [status:200 duration:0.015s]
2025-08-01 00:14:09,368 INFO     36 set_progress(5dc3b8426e2911f0848702420ae90a06), progress: 0.8200000000000001, progress_msg: 
2025-08-01 00:14:09,390 INFO     36 PUT http://es01:9200/ragflow_ebd47f2646b611f0821c02420ae90706/_bulk?refresh=false&timeout=60s [status:200 duration:0.010s]
2025-08-01 00:14:09,396 INFO     36 Indexing doc(Liheyu Zhang - Employment Reference Letter ********.pdf), page(0-2), chunks(5), elapsed: 0.09
2025-08-01 00:14:09,424 INFO     36 set_progress(5dc3b8426e2911f0848702420ae90a06), progress: 1.0, progress_msg: 00:14:09 Page(1~3): Indexing done (0.10s). Task done (5.82s)
2025-08-01 00:14:09,425 INFO     36 Chunk doc(Liheyu Zhang - Employment Reference Letter ********.pdf), page(0-2), chunks(5), token(698), elapsed:5.82
2025-08-01 00:14:09,428 INFO     36 handle_task done for task {"id": "5dc3b8426e2911f0848702420ae90a06", "doc_id": "5a9e36d86e2911f0aca702420ae90a06", "from_page": 0, "to_page": 2, "retry_count": 0, "kb_id": "1dde70d26e2911f08f4502420ae90a06", "parser_id": "paper", "parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Liheyu Zhang - Employment Reference Letter ********.pdf", "type": "pdf", "location": "Liheyu Zhang - Employment Reference Letter ********.pdf", "size": 160834, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1753978439448, "task_type": ""}
2025-08-01 00:14:17,816 INFO     36 task_executor_332ded9aa4f2_0 reported heartbeat: {"name": "task_executor_332ded9aa4f2_0", "now": "2025-08-01T00:14:17.803+08:00", "boot_at": "2025-08-01T00:11:47.688+08:00", "pending": 2, "lag": 0, "done": 1, "failed": 0, "current": {"5e7a352c6e2911f0bdae02420ae90a06": {"id": "5e7a352c6e2911f0bdae02420ae90a06", "doc_id": "52be32ce6e2911f0863602420ae90a06", "from_page": 0, "to_page": 4, "retry_count": 0, "kb_id": "1dde70d26e2911f08f4502420ae90a06", "parser_id": "paper", "parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Bank Holiday Swap Program Guide.pdf", "type": "pdf", "location": "Bank Holiday Swap Program Guide.pdf", "size": 482309, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": *************, "task_type": ""}, "5f24b84e6e2911f0983202420ae90a06": {"id": "5f24b84e6e2911f0983202420ae90a06", "doc_id": "4a88ed1a6e2911f0a36102420ae90a06", "from_page": 0, "to_page": 18, "retry_count": 0, "kb_id": "1dde70d26e2911f08f4502420ae90a06", "parser_id": "paper", "parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "2505.14059v1.pdf", "type": "pdf", "location": "2505.14059v1.pdf", "size": 20037471, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1753978441755, "task_type": ""}}}
2025-08-01 00:14:20,572 INFO     36 __images__ dedupe_chars cost 11.412308655679226s
2025-08-01 00:14:20,756 INFO     36 __ocr detecting boxes of a image cost (0.14943628758192062s)
2025-08-01 00:14:20,757 INFO     36 __ocr sorting 0 chars cost 0.0010384023189544678s
2025-08-01 00:14:21,842 INFO     36 __ocr recognize 106 boxes cost 1.0841106921434402s
2025-08-01 00:14:21,993 INFO     36 __ocr detecting boxes of a image cost (0.1497260183095932s)
2025-08-01 00:14:21,995 INFO     36 __ocr sorting 0 chars cost 0.0017465576529502869s
2025-08-01 00:14:23,189 INFO     36 __ocr recognize 102 boxes cost 1.1932577416300774s
2025-08-01 00:14:23,329 INFO     36 __ocr detecting boxes of a image cost (0.1390688791871071s)
2025-08-01 00:14:23,330 INFO     36 __ocr sorting 0 chars cost 0.0009682625532150269s
2025-08-01 00:14:24,378 INFO     36 __ocr recognize 91 boxes cost 1.0451499000191689s
2025-08-01 00:14:24,557 INFO     36 __ocr detecting boxes of a image cost (0.17816153168678284s)
2025-08-01 00:14:24,559 INFO     36 __ocr sorting 0 chars cost 0.0024064183235168457s
2025-08-01 00:14:26,077 INFO     36 __ocr recognize 219 boxes cost 1.515408381819725s
2025-08-01 00:14:26,219 INFO     36 __ocr detecting boxes of a image cost (0.14113061130046844s)
2025-08-01 00:14:26,220 INFO     36 __ocr sorting 0 chars cost 0.0009462237358093262s
2025-08-01 00:14:26,910 INFO     36 __ocr recognize 86 boxes cost 0.6877134442329407s
2025-08-01 00:14:27,079 INFO     36 __ocr detecting boxes of a image cost (0.16857963055372238s)
2025-08-01 00:14:27,081 INFO     36 __ocr sorting 0 chars cost 0.002034328877925873s
2025-08-01 00:14:28,394 INFO     36 __ocr recognize 178 boxes cost 1.312942810356617s
2025-08-01 00:14:28,403 INFO     36 set_progress(5f24b84e6e2911f0983202420ae90a06), progress: 0.19999999999999998, progress_msg: 
2025-08-01 00:14:28,567 INFO     36 __ocr detecting boxes of a image cost (0.16454126685857773s)
2025-08-01 00:14:28,569 INFO     36 __ocr sorting 0 chars cost 0.00110539048910141s
2025-08-01 00:14:29,746 INFO     36 __ocr recognize 100 boxes cost 1.1753341481089592s
2025-08-01 00:14:29,901 INFO     36 __ocr detecting boxes of a image cost (0.1542593240737915s)
2025-08-01 00:14:29,902 INFO     36 __ocr sorting 0 chars cost 0.001042008399963379s
2025-08-01 00:14:31,129 INFO     36 __ocr recognize 93 boxes cost 1.2244586870074272s
2025-08-01 00:14:31,298 INFO     36 __ocr detecting boxes of a image cost (0.16806352138519287s)
2025-08-01 00:14:31,300 INFO     36 __ocr sorting 0 chars cost 0.0017886608839035034s
2025-08-01 00:14:32,624 INFO     36 __ocr recognize 152 boxes cost 1.3171149790287018s
2025-08-01 00:14:32,768 INFO     36 __ocr detecting boxes of a image cost (0.14406124502420425s)
2025-08-01 00:14:32,770 INFO     36 __ocr sorting 0 chars cost 0.0011975541710853577s
2025-08-01 00:14:34,048 INFO     36 __ocr recognize 118 boxes cost 1.27372395247221s
2025-08-01 00:14:34,193 INFO     36 __ocr detecting boxes of a image cost (0.14342526346445084s)
2025-08-01 00:14:34,194 INFO     36 __ocr sorting 0 chars cost 0.001297496259212494s
2025-08-01 00:14:35,449 INFO     36 __ocr recognize 116 boxes cost 1.2505271509289742s
2025-08-01 00:14:35,571 INFO     36 __ocr detecting boxes of a image cost (0.1217777356505394s)
2025-08-01 00:14:35,572 INFO     36 __ocr sorting 0 chars cost 0.0004201233386993408s
2025-08-01 00:14:36,039 INFO     36 __ocr recognize 38 boxes cost 0.4646589010953903s
2025-08-01 00:14:36,052 INFO     36 set_progress(5f24b84e6e2911f0983202420ae90a06), progress: 0.39999999999999997, progress_msg: 
2025-08-01 00:14:36,240 INFO     36 __ocr detecting boxes of a image cost (0.1876182183623314s)
2025-08-01 00:14:36,242 INFO     36 __ocr sorting 0 chars cost 0.0014964565634727478s
2025-08-01 00:14:37,436 INFO     36 __ocr recognize 137 boxes cost 1.1929953843355179s
2025-08-01 00:14:37,588 INFO     36 __ocr detecting boxes of a image cost (0.15175996720790863s)
2025-08-01 00:14:37,590 INFO     36 __ocr sorting 0 chars cost 0.0009933114051818848s
2025-08-01 00:14:38,293 INFO     36 __ocr recognize 57 boxes cost 0.7010044753551483s
2025-08-01 00:14:38,421 INFO     36 __ocr detecting boxes of a image cost (0.12748397886753082s)
2025-08-01 00:14:38,422 INFO     36 __ocr sorting 0 chars cost 0.0007012411952018738s
2025-08-01 00:14:39,076 INFO     36 __ocr recognize 63 boxes cost 0.6510422974824905s
2025-08-01 00:14:39,251 INFO     36 __ocr detecting boxes of a image cost (0.17446915805339813s)
2025-08-01 00:14:39,253 INFO     36 __ocr sorting 0 chars cost 0.0014226436614990234s
2025-08-01 00:14:40,225 INFO     36 __ocr recognize 112 boxes cost 0.9706373438239098s
2025-08-01 00:14:40,384 INFO     36 __ocr detecting boxes of a image cost (0.15889570116996765s)
2025-08-01 00:14:40,386 INFO     36 __ocr sorting 0 chars cost 0.001038283109664917s
2025-08-01 00:14:41,379 INFO     36 __ocr recognize 61 boxes cost 0.9909884482622147s
2025-08-01 00:14:41,590 INFO     36 __ocr detecting boxes of a image cost (0.2095789685845375s)
2025-08-01 00:14:41,593 INFO     36 __ocr sorting 0 chars cost 0.0028260797262191772s
2025-08-01 00:14:43,074 INFO     36 __ocr recognize 243 boxes cost 1.480553038418293s
2025-08-01 00:14:43,086 INFO     36 set_progress(5f24b84e6e2911f0983202420ae90a06), progress: 0.6, progress_msg: 
2025-08-01 00:14:43,087 INFO     36 __images__ 18 pages cost 22.48114885389805s
2025-08-01 00:14:43,097 INFO     36 set_progress(5f24b84e6e2911f0983202420ae90a06), progress: None, progress_msg: 00:14:43 Page(1~19): OCR finished (33.97s)
2025-08-01 00:14:43,097 DEBUG    36 DiT API call with original image size: (1786, 2526)
2025-08-01 00:14:43,609 DEBUG    36 DiT API call successful for image size: (1786, 2526)
2025-08-01 00:14:43,610 DEBUG    36 DiT API response for page 0: boxes=14
2025-08-01 00:14:43,615 DEBUG    36 DiT raw API boxes for page 0 (first 2): [[916.841993560791, 1728.7742171957889, 1569.8643223571778, 2328.6439814479027], [213.00358261108397, 1889.8451750170962, 467.6998249053955, 1927.371676902872]]
2025-08-01 00:14:43,621 DEBUG    36 DiT raw API classes for page 0 (first 2): ['TEXT', 'TITLE']
2025-08-01 00:14:43,624 DEBUG    36 DiT raw API scores for page 0 (first 2): [0.9992436170578003, 0.9991883635520935]
2025-08-01 00:14:43,628 INFO     36 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-08-01 00:14:43,646 INFO     36 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-08-01 00:14:45,025 DEBUG    36 Applied OCR adjustment to 14 boxes
2025-08-01 00:14:45,026 DEBUG    36 DiT processed 14 boxes for page 0, first 3: [{'type': 'text', 'x0': 909.0, 'top': 1718.0, 'x1': 1581.0, 'bottom': 2329.0, 'score': 0.9992436170578003}, {'type': 'title', 'x0': 213.0, 'top': 1889.8451750170962, 'x1': 467.6998249053955, 'bottom': 1929.0, 'score': 0.9991883635520935}, {'type': 'text', 'x0': 912.0, 'top': 1560.0, 'x1': 1584.0, 'bottom': 1723.0, 'score': 0.9977949857711792}]
2025-08-01 00:14:45,029 DEBUG    36 DiT API call with original image size: (1786, 2526)
2025-08-01 00:14:45,499 DEBUG    36 DiT API call successful for image size: (1786, 2526)
2025-08-01 00:14:45,500 DEBUG    36 DiT API response for page 1: boxes=15
2025-08-01 00:14:45,504 DEBUG    36 DiT raw API boxes for page 1 (first 2): [[915.1876489257812, 322.39795350901954, 1512.652013244629, 357.2488204996529], [210.28503967285155, 1973.4537217522172, 485.7071230316162, 2010.282551975402]]
2025-08-01 00:14:45,509 DEBUG    36 DiT raw API classes for page 1 (first 2): ['TITLE', 'TITLE']
2025-08-01 00:14:45,512 DEBUG    36 DiT raw API scores for page 1 (first 2): [0.9995660185813904, 0.9994747042655945]
2025-08-01 00:14:46,970 DEBUG    36 Applied OCR adjustment to 15 boxes
2025-08-01 00:14:46,971 DEBUG    36 DiT processed 15 boxes for page 1, first 3: [{'type': 'title', 'x0': 912.0, 'top': 321.0, 'x1': 1512.652013244629, 'bottom': 358.0, 'score': 0.9995660185813904}, {'type': 'title', 'x0': 207.0, 'top': 1973.4537217522172, 'x1': 485.7071230316162, 'bottom': 2013.0, 'score': 0.9994747042655945}, {'type': 'text', 'x0': 205.0, 'top': 460.0, 'x1': 877.0, 'bottom': 1234.0, 'score': 0.9993601441383362}]
2025-08-01 00:14:46,974 DEBUG    36 DiT API call with original image size: (1786, 2526)
2025-08-01 00:14:47,434 DEBUG    36 DiT API call successful for image size: (1786, 2526)
2025-08-01 00:14:47,435 DEBUG    36 DiT API response for page 2: boxes=15
2025-08-01 00:14:47,436 DEBUG    36 DiT raw API boxes for page 2 (first 2): [[203.69971652984617, 1032.2795177024618, 866.3972225952148, 1982.5205907037464], [215.7005281829834, 2004.0150939779508, 421.6093418884277, 2040.4175238571368]]
2025-08-01 00:14:47,438 DEBUG    36 DiT raw API classes for page 2 (first 2): ['TEXT', 'TITLE']
2025-08-01 00:14:47,441 DEBUG    36 DiT raw API scores for page 2 (first 2): [0.9997368454933167, 0.9995293617248535]
2025-08-01 00:14:48,271 INFO     36 task_executor_332ded9aa4f2_0 reported heartbeat: {"name": "task_executor_332ded9aa4f2_0", "now": "2025-08-01T00:14:48.268+08:00", "boot_at": "2025-08-01T00:11:47.688+08:00", "pending": 2, "lag": 0, "done": 1, "failed": 0, "current": {"5e7a352c6e2911f0bdae02420ae90a06": {"id": "5e7a352c6e2911f0bdae02420ae90a06", "doc_id": "52be32ce6e2911f0863602420ae90a06", "from_page": 0, "to_page": 4, "retry_count": 0, "kb_id": "1dde70d26e2911f08f4502420ae90a06", "parser_id": "paper", "parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Bank Holiday Swap Program Guide.pdf", "type": "pdf", "location": "Bank Holiday Swap Program Guide.pdf", "size": 482309, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": *************, "task_type": ""}, "5f24b84e6e2911f0983202420ae90a06": {"id": "5f24b84e6e2911f0983202420ae90a06", "doc_id": "4a88ed1a6e2911f0a36102420ae90a06", "from_page": 0, "to_page": 18, "retry_count": 0, "kb_id": "1dde70d26e2911f08f4502420ae90a06", "parser_id": "paper", "parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "2505.14059v1.pdf", "type": "pdf", "location": "2505.14059v1.pdf", "size": 20037471, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1753978441755, "task_type": ""}}}
2025-08-01 00:14:48,637 DEBUG    36 Applied OCR adjustment to 15 boxes
2025-08-01 00:14:48,637 DEBUG    36 DiT processed 15 boxes for page 2, first 3: [{'type': 'text', 'x0': 202.0, 'top': 1032.2795177024618, 'x1': 877.0, 'bottom': 1982.5205907037464, 'score': 0.9997368454933167}, {'type': 'title', 'x0': 203.0, 'top': 1997.0, 'x1': 426.0, 'bottom': 2044.0, 'score': 0.9995293617248535}, {'type': 'text', 'x0': 909.0, 'top': 1087.0, 'x1': 1581.0, 'bottom': 1579.0, 'score': 0.9993253946304321}]
2025-08-01 00:14:48,639 DEBUG    36 DiT API call with original image size: (1786, 2526)
2025-08-01 00:14:49,098 DEBUG    36 DiT API call successful for image size: (1786, 2526)
2025-08-01 00:14:49,099 DEBUG    36 DiT API response for page 3: boxes=13
2025-08-01 00:14:49,104 DEBUG    36 DiT raw API boxes for page 3 (first 2): [[914.5194250488281, 1820.2714429293765, 1091.481976776123, 1856.4121436971566], [915.8891204833984, 2035.1083760957185, 1120.3635829162597, 2071.3927552402806]]
2025-08-01 00:14:49,107 DEBUG    36 DiT raw API classes for page 3 (first 2): ['TITLE', 'TITLE']
2025-08-01 00:14:49,112 DEBUG    36 DiT raw API scores for page 3 (first 2): [0.9996830224990845, 0.9995973706245422]
2025-08-01 00:14:50,876 DEBUG    36 Applied OCR adjustment to 13 boxes
2025-08-01 00:14:50,876 DEBUG    36 DiT processed 13 boxes for page 3, first 3: [{'type': 'title', 'x0': 914.5194250488281, 'top': 1818.0, 'x1': 1096.0, 'bottom': 1856.4121436971566, 'score': 0.9996830224990845}, {'type': 'title', 'x0': 915.8891204833984, 'top': 2028.0, 'x1': 1120.3635829162597, 'bottom': 2071.3927552402806, 'score': 0.9995973706245422}, {'type': 'title', 'x0': 202.0, 'top': 1737.0, 'x1': 720.0, 'bottom': 1781.0, 'score': 0.9995691180229187}]
2025-08-01 00:14:50,880 DEBUG    36 DiT API call with original image size: (1786, 2526)
2025-08-01 00:14:51,368 DEBUG    36 DiT API call successful for image size: (1786, 2526)
2025-08-01 00:14:51,369 DEBUG    36 DiT API response for page 4: boxes=24
2025-08-01 00:14:51,371 DEBUG    36 DiT raw API boxes for page 4 (first 2): [[931.4095193481445, 1707.4535184096276, 1568.1988043212891, 1961.3547483085003], [196.7462843322754, 1553.4210289264545, 1570.1413409423828, 1655.2219299802096]]
2025-08-01 00:14:51,375 DEBUG    36 DiT raw API classes for page 4 (first 2): ['TABLE', 'CAPTION']
2025-08-01 00:14:51,378 DEBUG    36 DiT raw API scores for page 4 (first 2): [0.9969550371170044, 0.9948081374168396]
2025-08-01 00:14:52,243 DEBUG    36 Applied OCR adjustment to 24 boxes
2025-08-01 00:14:52,243 DEBUG    36 DiT processed 24 boxes for page 4, first 3: [{'type': 'table', 'x0': 925.0, 'top': 1707.4535184096276, 'x1': 1568.1988043212891, 'bottom': 1961.3547483085003, 'score': 0.9969550371170044}, {'type': 'figure caption', 'x0': 196.7462843322754, 'top': 1552.0, 'x1': 1579.0, 'bottom': 1658.0, 'score': 0.9948081374168396}, {'type': 'figure', 'x0': 494.1676698684692, 'top': 1084.7032452894457, 'x1': 810.127158203125, 'bottom': 1511.6433374217713, 'score': 0.9908716082572937}]
2025-08-01 00:14:52,246 DEBUG    36 DiT API call with original image size: (1786, 2526)
2025-08-01 00:14:52,718 DEBUG    36 DiT API call successful for image size: (1786, 2526)
2025-08-01 00:14:52,719 DEBUG    36 DiT API response for page 5: boxes=15
2025-08-01 00:14:52,723 DEBUG    36 DiT raw API boxes for page 5 (first 2): [[918.681653137207, 894.4561029327958, 1147.982185974121, 931.0587828456564], [944.7147233581543, 1373.8233305832434, 1266.6283058166503, 1408.926657112587]]
2025-08-01 00:14:52,730 DEBUG    36 DiT raw API classes for page 5 (first 2): ['TITLE', 'TITLE']
2025-08-01 00:14:52,736 DEBUG    36 DiT raw API scores for page 5 (first 2): [0.9997579455375671, 0.9993778467178345]
2025-08-01 00:14:54,403 DEBUG    36 Applied OCR adjustment to 15 boxes
2025-08-01 00:14:54,404 DEBUG    36 DiT processed 15 boxes for page 5, first 3: [{'type': 'title', 'x0': 917.0, 'top': 894.4561029327958, 'x1': 1148.0, 'bottom': 931.0587828456564, 'score': 0.9997579455375671}, {'type': 'title', 'x0': 944.7147233581543, 'top': 1373.8233305832434, 'x1': 1268.0, 'bottom': 1413.0, 'score': 0.9993778467178345}, {'type': 'title', 'x0': 944.0, 'top': 2084.0, 'x1': 1318.6801388549804, 'bottom': 2121.0, 'score': 0.9986556768417358}]
2025-08-01 00:14:54,408 DEBUG    36 DiT API call with original image size: (1786, 2526)
2025-08-01 00:14:54,890 DEBUG    36 DiT API call successful for image size: (1786, 2526)
2025-08-01 00:14:54,891 DEBUG    36 DiT API response for page 6: boxes=17
2025-08-01 00:14:54,893 DEBUG    36 DiT raw API boxes for page 6 (first 2): [[213.7850053024292, 1930.844222415347, 453.4005629348755, 1967.7439376062043], [213.51147840499877, 1992.9870288150694, 621.017893371582, 2028.4466886242124]]
2025-08-01 00:14:54,898 DEBUG    36 DiT raw API classes for page 6 (first 2): ['TITLE', 'TITLE']
2025-08-01 00:14:54,900 DEBUG    36 DiT raw API scores for page 6 (first 2): [0.999194324016571, 0.9991276860237122]
2025-08-01 00:14:56,307 DEBUG    36 Applied OCR adjustment to 17 boxes
2025-08-01 00:14:56,307 DEBUG    36 DiT processed 17 boxes for page 6, first 3: [{'type': 'title', 'x0': 203.0, 'top': 1926.0, 'x1': 455.0, 'bottom': 1970.0, 'score': 0.999194324016571}, {'type': 'title', 'x0': 207.0, 'top': 1992.9870288150694, 'x1': 621.017893371582, 'bottom': 2031.0, 'score': 0.9991276860237122}, {'type': 'text', 'x0': 909.0, 'top': 1515.0, 'x1': 1584.0, 'bottom': 2331.072609494156, 'score': 0.9990696310997009}]
2025-08-01 00:14:56,310 DEBUG    36 DiT API call with original image size: (1786, 2526)
2025-08-01 00:14:56,785 DEBUG    36 DiT API call successful for image size: (1786, 2526)
2025-08-01 00:14:56,786 DEBUG    36 DiT API response for page 7: boxes=21
2025-08-01 00:14:56,790 DEBUG    36 DiT raw API boxes for page 7 (first 2): [[213.03189083099363, 2027.9900258517073, 519.9470221328735, 2064.0055873880965], [916.5437182617187, 1049.2444715423987, 1573.7508944702147, 1533.9828713009779]]
2025-08-01 00:14:56,792 DEBUG    36 DiT raw API classes for page 7 (first 2): ['TITLE', 'TEXT']
2025-08-01 00:14:56,797 DEBUG    36 DiT raw API scores for page 7 (first 2): [0.9991573095321655, 0.9986739158630371]
2025-08-01 00:14:58,226 DEBUG    36 Applied OCR adjustment to 21 boxes
2025-08-01 00:14:58,226 DEBUG    36 DiT processed 21 boxes for page 7, first 3: [{'type': 'title', 'x0': 210.0, 'top': 2027.9900258517073, 'x1': 524.0, 'bottom': 2066.0, 'score': 0.9991573095321655}, {'type': 'text', 'x0': 909.0, 'top': 1045.0, 'x1': 1584.0, 'bottom': 1539.0, 'score': 0.9986739158630371}, {'type': 'text', 'x0': 909.0, 'top': 1543.7649968397711, 'x1': 1581.0, 'bottom': 2152.571447073939, 'score': 0.9985032081604004}]
2025-08-01 00:14:58,228 DEBUG    36 DiT API call with original image size: (1786, 2526)
2025-08-01 00:14:58,697 DEBUG    36 DiT API call successful for image size: (1786, 2526)
2025-08-01 00:14:58,698 DEBUG    36 DiT API response for page 8: boxes=21
2025-08-01 00:14:58,701 DEBUG    36 DiT raw API boxes for page 8 (first 2): [[211.92058029174805, 1427.6892264692472, 441.6935299682617, 1462.9700380522627], [212.91358222961426, 1504.4527390376325, 864.2802037811279, 2103.596477640086]]
2025-08-01 00:14:58,707 DEBUG    36 DiT raw API classes for page 8 (first 2): ['TITLE', 'TEXT']
2025-08-01 00:14:58,712 DEBUG    36 DiT raw API scores for page 8 (first 2): [0.9993867874145508, 0.9991223216056824]
2025-08-01 00:15:00,327 DEBUG    36 Applied OCR adjustment to 21 boxes
2025-08-01 00:15:00,327 DEBUG    36 DiT processed 21 boxes for page 8, first 3: [{'type': 'title', 'x0': 207.0, 'top': 1427.6892264692472, 'x1': 441.6935299682617, 'bottom': 1466.0, 'score': 0.9993867874145508}, {'type': 'text', 'x0': 202.0, 'top': 1492.0, 'x1': 880.0, 'bottom': 2105.0, 'score': 0.9991223216056824}, {'type': 'title', 'x0': 912.0, 'top': 818.0, 'x1': 1093.0, 'bottom': 853.079933874803, 'score': 0.9986202716827393}]
2025-08-01 00:15:00,330 DEBUG    36 DiT API call with original image size: (1786, 2526)
2025-08-01 00:15:00,801 DEBUG    36 DiT API call successful for image size: (1786, 2526)
2025-08-01 00:15:00,802 DEBUG    36 DiT API response for page 9: boxes=5
2025-08-01 00:15:00,805 DEBUG    36 DiT raw API boxes for page 9 (first 2): [[875.5693583679199, 2379.1195311204824, 915.247740020752, 2414.3477242467256], [904.373908920288, 262.1379268251616, 1583.9612026977538, 2339.8468053195456]]
2025-08-01 00:15:00,808 DEBUG    36 DiT raw API classes for page 9 (first 2): ['FOOTER', 'TEXT']
2025-08-01 00:15:00,812 DEBUG    36 DiT raw API scores for page 9 (first 2): [0.9933741688728333, 0.9785705208778381]
2025-08-01 00:15:02,319 DEBUG    36 Applied OCR adjustment to 5 boxes
2025-08-01 00:15:02,320 DEBUG    36 DiT processed 5 boxes for page 9, first 3: [{'type': 'footer', 'x0': 874.0, 'top': 2379.1195311204824, 'x1': 917.0, 'bottom': 2415.0, 'score': 0.9933741688728333}, {'type': 'text', 'x0': 904.373908920288, 'top': 253.0, 'x1': 1584.0, 'bottom': 2339.8468053195456, 'score': 0.9785705208778381}, {'type': 'text', 'x0': 192.83780826568602, 'top': 282.0, 'x1': 877.8881115722656, 'bottom': 2326.0, 'score': 0.9442363381385803}]
2025-08-01 00:15:02,322 DEBUG    36 DiT API call with original image size: (1786, 2526)
2025-08-01 00:15:02,811 DEBUG    36 DiT API call successful for image size: (1786, 2526)
2025-08-01 00:15:02,812 DEBUG    36 DiT API response for page 10: boxes=21
2025-08-01 00:15:02,817 DEBUG    36 DiT raw API boxes for page 10 (first 2): [[873.5062989044189, 2380.7089902032903, 910.9329677581787, 2413.5979793963443], [899.9493784332275, 246.4133917608691, 1564.89529296875, 2354.6167788720572]]
2025-08-01 00:15:02,821 DEBUG    36 DiT raw API classes for page 10 (first 2): ['FOOTER', 'TEXT']
2025-08-01 00:15:02,824 DEBUG    36 DiT raw API scores for page 10 (first 2): [0.9904443621635437, 0.9478646516799927]
2025-08-01 00:15:04,364 DEBUG    36 Applied OCR adjustment to 21 boxes
2025-08-01 00:15:04,365 DEBUG    36 DiT processed 21 boxes for page 10, first 3: [{'type': 'footer', 'x0': 872.0, 'top': 2380.7089902032903, 'x1': 914.0, 'bottom': 2415.0, 'score': 0.9904443621635437}, {'type': 'text', 'x0': 899.9493784332275, 'top': 221.0, 'x1': 1584.0, 'bottom': 2354.6167788720572, 'score': 0.9478646516799927}, {'type': 'text', 'x0': 207.0, 'top': 218.0, 'x1': 877.0, 'bottom': 421.0, 'score': 0.9470903277397156}]
2025-08-01 00:15:04,367 DEBUG    36 DiT API call with original image size: (1786, 2526)
2025-08-01 00:15:04,819 DEBUG    36 DiT API call successful for image size: (1786, 2526)
2025-08-01 00:15:04,820 DEBUG    36 DiT API response for page 11: boxes=8
2025-08-01 00:15:04,823 DEBUG    36 DiT raw API boxes for page 11 (first 2): [[218.57546466827392, 952.4820603590745, 871.2786361694335, 1179.7909991115093], [209.31090988159178, 572.1568090302242, 861.9826391601562, 700.4866818698711]]
2025-08-01 00:15:04,827 DEBUG    36 DiT raw API classes for page 11 (first 2): ['TEXT', 'TEXT']
2025-08-01 00:15:04,829 DEBUG    36 DiT raw API scores for page 11 (first 2): [0.9947901964187622, 0.9940053820610046]
2025-08-01 00:15:05,545 DEBUG    36 Applied OCR adjustment to 8 boxes
2025-08-01 00:15:05,545 DEBUG    36 DiT processed 8 boxes for page 11, first 3: [{'type': 'text', 'x0': 202.0, 'top': 944.0, 'x1': 880.0, 'bottom': 1181.0, 'score': 0.9947901964187622}, {'type': 'text', 'x0': 207.0, 'top': 566.0, 'x1': 874.0, 'bottom': 705.0, 'score': 0.9940053820610046}, {'type': 'text', 'x0': 207.0, 'top': 727.5315462714481, 'x1': 877.0, 'bottom': 927.7049696540325, 'score': 0.9939416646957397}]
2025-08-01 00:15:05,547 DEBUG    36 DiT API call with original image size: (1786, 2526)
2025-08-01 00:15:06,029 DEBUG    36 DiT API call successful for image size: (1786, 2526)
2025-08-01 00:15:06,030 DEBUG    36 DiT API response for page 12: boxes=19
2025-08-01 00:15:06,032 DEBUG    36 DiT raw API boxes for page 12 (first 2): [[941.3004318237305, 218.1304770957886, 1543.4707055664062, 721.3939986089812], [207.52495416641236, 541.2392057626254, 569.2280220794678, 579.3321398828642]]
2025-08-01 00:15:06,035 DEBUG    36 DiT raw API classes for page 12 (first 2): ['TABLE', 'TITLE']
2025-08-01 00:15:06,037 DEBUG    36 DiT raw API scores for page 12 (first 2): [0.9992374181747437, 0.9992094039916992]
2025-08-01 00:15:07,425 DEBUG    36 Applied OCR adjustment to 19 boxes
2025-08-01 00:15:07,425 DEBUG    36 DiT processed 19 boxes for page 12, first 3: [{'type': 'table', 'x0': 941.3004318237305, 'top': 218.1304770957886, 'x1': 1543.4707055664062, 'bottom': 721.3939986089812, 'score': 0.9992374181747437}, {'type': 'title', 'x0': 207.52495416641236, 'top': 541.2392057626254, 'x1': 569.2280220794678, 'bottom': 579.3321398828642, 'score': 0.9992094039916992}, {'type': 'title', 'x0': 207.65460649490356, 'top': 1923.0, 'x1': 519.0, 'bottom': 1968.0, 'score': 0.9990347623825073}]
2025-08-01 00:15:07,427 DEBUG    36 DiT API call with original image size: (1786, 2526)
2025-08-01 00:15:07,885 DEBUG    36 DiT API call successful for image size: (1786, 2526)
2025-08-01 00:15:07,886 DEBUG    36 DiT API response for page 13: boxes=15
2025-08-01 00:15:07,890 DEBUG    36 DiT raw API boxes for page 13 (first 2): [[914.0591354370117, 1894.8889131014794, 1368.2664656066895, 1936.3514391345116], [1154.9436235046387, 220.40058522515332, 1501.4773860168457, 633.084687979215]]
2025-08-01 00:15:07,894 DEBUG    36 DiT raw API classes for page 13 (first 2): ['TITLE', 'FIGURE']
2025-08-01 00:15:07,897 DEBUG    36 DiT raw API scores for page 13 (first 2): [0.998823344707489, 0.9984968900680542]
2025-08-01 00:15:08,806 DEBUG    36 Applied OCR adjustment to 15 boxes
2025-08-01 00:15:08,806 DEBUG    36 DiT processed 15 boxes for page 13, first 3: [{'type': 'title', 'x0': 914.0591354370117, 'top': 1894.8889131014794, 'x1': 1369.0, 'bottom': 1937.0, 'score': 0.998823344707489}, {'type': 'figure', 'x0': 1154.9436235046387, 'top': 220.40058522515332, 'x1': 1501.4773860168457, 'bottom': 633.084687979215, 'score': 0.9984968900680542}, {'type': 'figure', 'x0': 1154.4023948669433, 'top': 647.1232513508683, 'x1': 1499.8705964660644, 'bottom': 1060.3831581500226, 'score': 0.998408854007721}]
2025-08-01 00:15:08,809 DEBUG    36 DiT API call with original image size: (1786, 2526)
2025-08-01 00:15:09,318 DEBUG    36 DiT API call successful for image size: (1786, 2526)
2025-08-01 00:15:09,319 DEBUG    36 DiT API response for page 14: boxes=26
2025-08-01 00:15:09,321 DEBUG    36 DiT raw API boxes for page 14 (first 2): [[215.3740468597412, 2136.6152408508783, 1568.656368713379, 2238.150323275862], [872.587763595581, 2379.4207922330897, 916.567904586792, 2413.9592200969823]]
2025-08-01 00:15:09,323 DEBUG    36 DiT raw API classes for page 14 (first 2): ['CAPTION', 'FOOTER']
2025-08-01 00:15:09,326 DEBUG    36 DiT raw API scores for page 14 (first 2): [0.9886342287063599, 0.9792050719261169]
2025-08-01 00:15:10,144 DEBUG    36 Applied OCR adjustment to 26 boxes
2025-08-01 00:15:10,144 DEBUG    36 DiT processed 26 boxes for page 14, first 3: [{'type': 'figure caption', 'x0': 207.0, 'top': 2134.0, 'x1': 1579.0, 'bottom': 2242.0, 'score': 0.9886342287063599}, {'type': 'footer', 'x0': 872.587763595581, 'top': 2379.4207922330897, 'x1': 916.567904586792, 'bottom': 2413.9592200969823, 'score': 0.9792050719261169}, {'type': 'title', 'x0': 901.0, 'top': 289.0, 'x1': 981.0, 'bottom': 318.0, 'score': 0.967642068862915}]
2025-08-01 00:15:10,147 DEBUG    36 DiT API call with original image size: (1786, 2526)
2025-08-01 00:15:10,624 DEBUG    36 DiT API call successful for image size: (1786, 2526)
2025-08-01 00:15:10,625 DEBUG    36 DiT API response for page 15: boxes=42
2025-08-01 00:15:10,628 DEBUG    36 DiT raw API boxes for page 15 (first 2): [[726.9946029663085, 302.5769829788005, 1065.8542829895018, 337.14035689862396], [341.06417839050295, 302.08975106398685, 518.1469804382324, 338.48161635664474]]
2025-08-01 00:15:10,633 DEBUG    36 DiT raw API classes for page 15 (first 2): ['TITLE', 'TITLE']
2025-08-01 00:15:10,635 DEBUG    36 DiT raw API scores for page 15 (first 2): [0.9877061247825623, 0.9731138348579407]
2025-08-01 00:15:11,853 DEBUG    36 Applied OCR adjustment to 42 boxes
2025-08-01 00:15:11,853 DEBUG    36 DiT processed 42 boxes for page 15, first 3: [{'type': 'title', 'x0': 720.0, 'top': 302.5769829788005, 'x1': 1068.0, 'bottom': 342.0, 'score': 0.9877061247825623}, {'type': 'title', 'x0': 340.0, 'top': 302.08975106398685, 'x1': 521.0, 'bottom': 342.0, 'score': 0.9731138348579407}, {'type': 'footer', 'x0': 872.6979987335205, 'top': 2376.0, 'x1': 918.0, 'bottom': 2415.1356379258536, 'score': 0.9695018529891968}]
2025-08-01 00:15:11,858 DEBUG    36 DiT API call with original image size: (1786, 2526)
2025-08-01 00:15:12,323 DEBUG    36 DiT API call successful for image size: (1786, 2526)
2025-08-01 00:15:12,324 DEBUG    36 DiT API response for page 16: boxes=4
2025-08-01 00:15:12,329 DEBUG    36 DiT raw API boxes for page 16 (first 2): [[871.3135189819336, 2376.748974220822, 917.0371192932129, 2416.3453171626325], [209.4599112701416, 257.5358566162757, 1580.6364346313476, 2096.410514042295]]
2025-08-01 00:15:12,335 DEBUG    36 DiT raw API classes for page 16 (first 2): ['FOOTER', 'TABLE']
2025-08-01 00:15:12,339 DEBUG    36 DiT raw API scores for page 16 (first 2): [0.9828945398330688, 0.8937831521034241]
2025-08-01 00:15:13,576 DEBUG    36 Applied OCR adjustment to 4 boxes
2025-08-01 00:15:13,577 DEBUG    36 DiT processed 4 boxes for page 16, first 3: [{'type': 'footer', 'x0': 871.3135189819336, 'top': 2376.748974220822, 'x1': 917.0371192932129, 'bottom': 2416.3453171626325, 'score': 0.9828945398330688}, {'type': 'table', 'x0': 209.4599112701416, 'top': 257.5358566162757, 'x1': 1580.6364346313476, 'bottom': 2102.0, 'score': 0.8937831521034241}, {'type': 'text', 'x0': 205.0, 'top': 2066.0, 'x1': 1587.1082864379882, 'bottom': 2210.4567932614596, 'score': 0.8074193000793457}]
2025-08-01 00:15:13,579 DEBUG    36 DiT API call with original image size: (1786, 2526)
2025-08-01 00:15:14,034 DEBUG    36 DiT API call successful for image size: (1786, 2526)
2025-08-01 00:15:14,035 DEBUG    36 DiT API response for page 17: boxes=4
2025-08-01 00:15:14,036 DEBUG    36 DiT raw API boxes for page 17 (first 2): [[376.9895236968994, 226.11234597122635, 1404.9450616455078, 1020.9257224814012], [199.04745986938477, 2198.424206316313, 1574.6469467163085, 2303.3295963196283]]
2025-08-01 00:15:14,038 DEBUG    36 DiT raw API classes for page 17 (first 2): ['TABLE', 'CAPTION']
2025-08-01 00:15:14,041 DEBUG    36 DiT raw API scores for page 17 (first 2): [0.9996875524520874, 0.999538779258728]
2025-08-01 00:15:15,713 DEBUG    36 Applied OCR adjustment to 4 boxes
2025-08-01 00:15:15,713 DEBUG    36 DiT processed 4 boxes for page 17, first 3: [{'type': 'table', 'x0': 376.9895236968994, 'top': 226.11234597122635, 'x1': 1404.9450616455078, 'bottom': 1020.9257224814012, 'score': 0.9996875524520874}, {'type': 'figure caption', 'x0': 199.04745986938477, 'top': 2198.424206316313, 'x1': 1579.0, 'bottom': 2305.0, 'score': 0.999538779258728}, {'type': 'table', 'x0': 497.72363876342774, 'top': 1039.3967092498858, 'x1': 1317.0357412719727, 'bottom': 2183.019267034151, 'score': 0.9993970394134521}]
2025-08-01 00:15:15,716 DEBUG    36 DiT original boxes for page 0 (before scale_factor=3 division): [{'type': 'text', 'x0': 909.0, 'top': 1718.0, 'x1': 1581.0, 'bottom': 2329.0, 'score': 0.9992436170578003}, {'type': 'title', 'x0': 213.0, 'top': 1889.8451750170962, 'x1': 467.6998249053955, 'bottom': 1929.0, 'score': 0.9991883635520935}]
2025-08-01 00:15:15,717 DEBUG    36 DiT scaled boxes for page 0 (after scale_factor=3 division): [{'type': 'text', 'score': 0.9992436170578003, 'x0': 303.0, 'x1': 527.0, 'top': 572.6666666666666, 'bottom': 776.3333333333334, 'page_number': 0}, {'type': 'title', 'score': 0.9991883635520935, 'x0': 71.0, 'x1': 155.89994163513182, 'top': 629.9483916723653, 'bottom': 643.0, 'page_number': 0}]
2025-08-01 00:15:15,719 DEBUG    36 Image 0 dimensions: (1786, 2526)
2025-08-01 00:15:15,721 DEBUG    36 OCR boxes sample for page 0: [{'x0': 72.66666666666667, 'x1': 523.6666666666666, 'top': 79.0, 'text': 'Dolphin: Document Image Parsing via Heterogeneous Anchor Prompting', 'bottom': 94.0, 'page_number': 1}, {'x0': 118.66666666666667, 'x1': 475.0, 'top': 118.33333333333333, 'text': 'Hao Feng*, Shu Wei*, Xiang Fei*, Wei Shi*t, Yingdong Han, Lei Liao,', 'bottom': 133.33333333333334, 'page_number': 1}]
2025-08-01 00:15:15,731 DEBUG    36 DiT original boxes for page 1 (before scale_factor=3 division): [{'type': 'title', 'x0': 912.0, 'top': 321.0, 'x1': 1512.652013244629, 'bottom': 358.0, 'score': 0.9995660185813904}, {'type': 'title', 'x0': 207.0, 'top': 1973.4537217522172, 'x1': 485.7071230316162, 'bottom': 2013.0, 'score': 0.9994747042655945}]
2025-08-01 00:15:15,731 DEBUG    36 DiT scaled boxes for page 1 (after scale_factor=3 division): [{'type': 'title', 'score': 0.9995660185813904, 'x0': 304.0, 'x1': 504.2173377482097, 'top': 107.0, 'bottom': 119.33333333333333, 'page_number': 1}, {'type': 'title', 'score': 0.9994747042655945, 'x0': 69.0, 'x1': 161.90237434387208, 'top': 657.817907250739, 'bottom': 671.0, 'page_number': 1}]
2025-08-01 00:15:15,735 DEBUG    36 Image 1 dimensions: (1786, 2526)
2025-08-01 00:15:15,738 DEBUG    36 OCR boxes sample for page 1: [{'x0': 69.0, 'x1': 289.6666666666667, 'top': 72.0, 'text': 'feature fusion. These methods (Blecher et al.; Kim', 'bottom': 84.33333333333333, 'page_number': 2}, {'x0': 304.6666666666667, 'x1': 525.3333333333334, 'top': 73.66666666666667, 'text': 'language models to directly generate structured', 'bottom': 86.0, 'page_number': 2}]
2025-08-01 00:15:15,751 DEBUG    36 DiT original boxes for page 2 (before scale_factor=3 division): [{'type': 'text', 'x0': 202.0, 'top': 1032.2795177024618, 'x1': 877.0, 'bottom': 1982.5205907037464, 'score': 0.9997368454933167}, {'type': 'title', 'x0': 203.0, 'top': 1997.0, 'x1': 426.0, 'bottom': 2044.0, 'score': 0.9995293617248535}]
2025-08-01 00:15:15,751 DEBUG    36 DiT scaled boxes for page 2 (after scale_factor=3 division): [{'type': 'text', 'score': 0.9997368454933167, 'x0': 67.33333333333333, 'x1': 292.3333333333333, 'top': 344.0931725674873, 'bottom': 660.8401969012488, 'page_number': 2}, {'type': 'title', 'score': 0.9995293617248535, 'x0': 67.66666666666667, 'x1': 142.0, 'top': 665.6666666666666, 'bottom': 681.3333333333334, 'page_number': 2}]
2025-08-01 00:15:15,753 DEBUG    36 Image 2 dimensions: (1786, 2526)
2025-08-01 00:15:15,755 DEBUG    36 OCR boxes sample for page 2: [{'x0': 375.6666666666667, 'x1': 407.6666666666667, 'top': 74.66666666666667, 'text': 'TextParagraph', 'bottom': 80.66666666666667, 'page_number': 3}, {'x0': 435.0, 'x1': 446.6666666666667, 'top': 74.66666666666667, 'text': 'Table', 'bottom': 81.66666666666667, 'page_number': 3}]
2025-08-01 00:15:15,765 DEBUG    36 DiT original boxes for page 3 (before scale_factor=3 division): [{'type': 'title', 'x0': 914.5194250488281, 'top': 1818.0, 'x1': 1096.0, 'bottom': 1856.4121436971566, 'score': 0.9996830224990845}, {'type': 'title', 'x0': 915.8891204833984, 'top': 2028.0, 'x1': 1120.3635829162597, 'bottom': 2071.3927552402806, 'score': 0.9995973706245422}]
2025-08-01 00:15:15,765 DEBUG    36 DiT scaled boxes for page 3 (after scale_factor=3 division): [{'type': 'title', 'score': 0.9996830224990845, 'x0': 304.83980834960937, 'x1': 365.3333333333333, 'top': 606.0, 'bottom': 618.8040478990522, 'page_number': 3}, {'type': 'title', 'score': 0.9995973706245422, 'x0': 305.29637349446614, 'x1': 373.45452763875323, 'top': 676.0, 'bottom': 690.4642517467602, 'page_number': 3}]
2025-08-01 00:15:15,767 DEBUG    36 Image 3 dimensions: (1786, 2526)
2025-08-01 00:15:15,770 DEBUG    36 OCR boxes sample for page 3: [{'x0': 279.3333333333333, 'x1': 350.0, 'top': 68.33333333333333, 'text': 'Plain Doc (ED ↓)', 'bottom': 80.66666666666667, 'page_number': 4}, {'x0': 369.3333333333333, 'x1': 455.3333333333333, 'top': 70.0, 'text': 'Complex Doc (ED ↓)', 'bottom': 81.66666666666667, 'page_number': 4}]
2025-08-01 00:15:15,788 DEBUG    36 DiT original boxes for page 4 (before scale_factor=3 division): [{'type': 'table', 'x0': 925.0, 'top': 1707.4535184096276, 'x1': 1568.1988043212891, 'bottom': 1961.3547483085003, 'score': 0.9969550371170044}, {'type': 'figure caption', 'x0': 196.7462843322754, 'top': 1552.0, 'x1': 1579.0, 'bottom': 1658.0, 'score': 0.9948081374168396}]
2025-08-01 00:15:15,788 DEBUG    36 DiT scaled boxes for page 4 (after scale_factor=3 division): [{'type': 'table', 'score': 0.9969550371170044, 'x0': 308.3333333333333, 'x1': 522.7329347737631, 'top': 569.1511728032092, 'bottom': 653.7849161028334, 'page_number': 4}, {'type': 'figure caption', 'score': 0.9948081374168396, 'x0': 65.58209477742513, 'x1': 526.3333333333334, 'top': 517.3333333333334, 'bottom': 552.6666666666666, 'page_number': 4}]
2025-08-01 00:15:15,791 DEBUG    36 Image 4 dimensions: (1786, 2526)
2025-08-01 00:15:15,793 DEBUG    36 OCR boxes sample for page 4: [{'x0': 168.33333333333334, 'x1': 265.0, 'top': 69.33333333333333, 'text': 'Reading Order & Layout', 'bottom': 80.66666666666667, 'page_number': 5}, {'x0': 317.0, 'x1': 343.6666666666667, 'top': 69.33333333333333, 'text': 'Spans', 'bottom': 81.66666666666667, 'page_number': 5}]
2025-08-01 00:15:15,802 DEBUG    36 DiT original boxes for page 5 (before scale_factor=3 division): [{'type': 'title', 'x0': 917.0, 'top': 894.4561029327958, 'x1': 1148.0, 'bottom': 931.0587828456564, 'score': 0.9997579455375671}, {'type': 'title', 'x0': 944.7147233581543, 'top': 1373.8233305832434, 'x1': 1268.0, 'bottom': 1413.0, 'score': 0.9993778467178345}]
2025-08-01 00:15:15,802 DEBUG    36 DiT scaled boxes for page 5 (after scale_factor=3 division): [{'type': 'title', 'score': 0.9997579455375671, 'x0': 305.6666666666667, 'x1': 382.6666666666667, 'top': 298.1520343109319, 'bottom': 310.3529276152188, 'page_number': 5}, {'type': 'title', 'score': 0.9993778467178345, 'x0': 314.9049077860514, 'x1': 422.6666666666667, 'top': 457.9411101944145, 'bottom': 471.0, 'page_number': 5}]
2025-08-01 00:15:15,807 DEBUG    36 Image 5 dimensions: (1786, 2526)
2025-08-01 00:15:15,810 DEBUG    36 OCR boxes sample for page 5: [{'x0': 218.66666666666666, 'x1': 312.0, 'top': 68.33333333333333, 'text': 'Text Paragraph (ED ↓)', 'bottom': 83.33333333333333, 'page_number': 6}, {'x0': 444.6666666666667, 'x1': 508.6666666666667, 'top': 68.33333333333333, 'text': 'Table (TEDS ↑)', 'bottom': 80.66666666666667, 'page_number': 6}]
2025-08-01 00:15:15,848 DEBUG    36 DiT original boxes for page 6 (before scale_factor=3 division): [{'type': 'title', 'x0': 203.0, 'top': 1926.0, 'x1': 455.0, 'bottom': 1970.0, 'score': 0.999194324016571}, {'type': 'title', 'x0': 207.0, 'top': 1992.9870288150694, 'x1': 621.017893371582, 'bottom': 2031.0, 'score': 0.9991276860237122}]
2025-08-01 00:15:15,848 DEBUG    36 DiT scaled boxes for page 6 (after scale_factor=3 division): [{'type': 'title', 'score': 0.999194324016571, 'x0': 67.66666666666667, 'x1': 151.66666666666666, 'top': 642.0, 'bottom': 656.6666666666666, 'page_number': 6}, {'type': 'title', 'score': 0.9991276860237122, 'x0': 69.0, 'x1': 207.00596445719398, 'top': 664.3290096050231, 'bottom': 677.0, 'page_number': 6}]
2025-08-01 00:15:15,851 DEBUG    36 Image 6 dimensions: (1786, 2526)
2025-08-01 00:15:15,854 DEBUG    36 OCR boxes sample for page 6: [{'x0': 102.66666666666667, 'x1': 125.0, 'top': 73.66666666666667, 'text': 'dins are n', 'bottom': 83.33333333333333, 'page_number': 7}, {'x0': 149.66666666666666, 'x1': 168.33333333333334, 'top': 73.66666666666667, 'text': 'ablteotfo', 'bottom': 83.33333333333333, 'page_number': 7}]
2025-08-01 00:15:15,878 DEBUG    36 DiT original boxes for page 7 (before scale_factor=3 division): [{'type': 'title', 'x0': 210.0, 'top': 2027.9900258517073, 'x1': 524.0, 'bottom': 2066.0, 'score': 0.9991573095321655}, {'type': 'text', 'x0': 909.0, 'top': 1045.0, 'x1': 1584.0, 'bottom': 1539.0, 'score': 0.9986739158630371}]
2025-08-01 00:15:15,878 DEBUG    36 DiT scaled boxes for page 7 (after scale_factor=3 division): [{'type': 'title', 'score': 0.9991573095321655, 'x0': 70.0, 'x1': 174.66666666666666, 'top': 675.9966752839024, 'bottom': 688.6666666666666, 'page_number': 7}, {'type': 'text', 'score': 0.9986739158630371, 'x0': 303.0, 'x1': 528.0, 'top': 348.3333333333333, 'bottom': 513.0, 'page_number': 7}]
2025-08-01 00:15:15,880 DEBUG    36 Image 7 dimensions: (1786, 2526)
2025-08-01 00:15:15,883 DEBUG    36 OCR boxes sample for page 7: [{'x0': 270.3333333333333, 'x1': 356.0, 'top': 73.66666666666667, 'text': 'hl', 'bottom': 79.66666666666667, 'page_number': 8}, {'x0': 268.3333333333333, 'x1': 355.3333333333333, 'top': 151.0, 'text': 'a', 'bottom': 157.0, 'page_number': 8}]
2025-08-01 00:15:15,900 DEBUG    36 DiT original boxes for page 8 (before scale_factor=3 division): [{'type': 'title', 'x0': 207.0, 'top': 1427.6892264692472, 'x1': 441.6935299682617, 'bottom': 1466.0, 'score': 0.9993867874145508}, {'type': 'text', 'x0': 202.0, 'top': 1492.0, 'x1': 880.0, 'bottom': 2105.0, 'score': 0.9991223216056824}]
2025-08-01 00:15:15,901 DEBUG    36 DiT scaled boxes for page 8 (after scale_factor=3 division): [{'type': 'title', 'score': 0.9993867874145508, 'x0': 69.0, 'x1': 147.23117665608723, 'top': 475.89640882308237, 'bottom': 488.6666666666667, 'page_number': 8}, {'type': 'text', 'score': 0.9991223216056824, 'x0': 67.33333333333333, 'x1': 293.3333333333333, 'top': 497.3333333333333, 'bottom': 701.6666666666666, 'page_number': 8}]
2025-08-01 00:15:15,907 DEBUG    36 Image 8 dimensions: (1786, 2526)
2025-08-01 00:15:15,911 DEBUG    36 OCR boxes sample for page 8: [{'x0': 263.0, 'x1': 285.3333333333333, 'top': 71.0, 'text': 'speedup', 'bottom': 80.66666666666667, 'page_number': 9}, {'x0': 304.0, 'x1': 525.3333333333334, 'top': 72.0, 'text': 'horizontal text layout, showing limited capability', 'bottom': 84.33333333333333, 'page_number': 9}]
2025-08-01 00:15:15,936 DEBUG    36 DiT original boxes for page 9 (before scale_factor=3 division): [{'type': 'footer', 'x0': 874.0, 'top': 2379.1195311204824, 'x1': 917.0, 'bottom': 2415.0, 'score': 0.9933741688728333}, {'type': 'text', 'x0': 904.373908920288, 'top': 253.0, 'x1': 1584.0, 'bottom': 2339.8468053195456, 'score': 0.9785705208778381}]
2025-08-01 00:15:15,936 DEBUG    36 DiT scaled boxes for page 9 (after scale_factor=3 division): [{'type': 'footer', 'score': 0.9933741688728333, 'x0': 291.3333333333333, 'x1': 305.6666666666667, 'top': 793.0398437068275, 'bottom': 805.0, 'page_number': 9}, {'type': 'text', 'score': 0.9785705208778381, 'x0': 301.457969640096, 'x1': 528.0, 'top': 84.33333333333333, 'bottom': 779.9489351065153, 'page_number': 9}]
2025-08-01 00:15:15,939 DEBUG    36 Image 9 dimensions: (1786, 2526)
2025-08-01 00:15:15,942 DEBUG    36 OCR boxes sample for page 9: [{'x0': 304.0, 'x1': 524.3333333333334, 'top': 72.66666666666667, 'text': 'Chenglong Liu, Haoran Wei, Jinyue Chen, Lingyu', 'bottom': 85.0, 'page_number': 10}, {'x0': 69.0, 'x1': 290.6666666666667, 'top': 73.66666666666667, 'text': 'Hao Feng, Qi Liu, Hao Liu, Jingqun Tang, Wengang', 'bottom': 86.0, 'page_number': 10}]
2025-08-01 00:15:15,953 DEBUG    36 DiT original boxes for page 10 (before scale_factor=3 division): [{'type': 'footer', 'x0': 872.0, 'top': 2380.7089902032903, 'x1': 914.0, 'bottom': 2415.0, 'score': 0.9904443621635437}, {'type': 'text', 'x0': 899.9493784332275, 'top': 221.0, 'x1': 1584.0, 'bottom': 2354.6167788720572, 'score': 0.9478646516799927}]
2025-08-01 00:15:15,954 DEBUG    36 DiT scaled boxes for page 10 (after scale_factor=3 division): [{'type': 'footer', 'score': 0.9904443621635437, 'x0': 290.6666666666667, 'x1': 304.6666666666667, 'top': 793.5696634010968, 'bottom': 805.0, 'page_number': 10}, {'type': 'text', 'score': 0.9478646516799927, 'x0': 299.98312614440914, 'x1': 528.0, 'top': 73.66666666666667, 'bottom': 784.8722596240191, 'page_number': 10}]
2025-08-01 00:15:15,957 DEBUG    36 Image 10 dimensions: (1786, 2526)
2025-08-01 00:15:15,960 DEBUG    36 OCR boxes sample for page 10: [{'x0': 69.0, 'x1': 289.6666666666667, 'top': 72.66666666666667, 'text': 'Jingqun Tang, Qi Liu, Yongjie Ye, Jinghui Lu, Shu', 'bottom': 85.0, 'page_number': 11}, {'x0': 304.6666666666667, 'x1': 525.3333333333334, 'top': 73.66666666666667, 'text': 'Peng Wang, Zhaohai Li, Jun Tang, Humen Zhong, Fei', 'bottom': 86.0, 'page_number': 11}]
2025-08-01 00:15:15,973 DEBUG    36 DiT original boxes for page 11 (before scale_factor=3 division): [{'type': 'text', 'x0': 202.0, 'top': 944.0, 'x1': 880.0, 'bottom': 1181.0, 'score': 0.9947901964187622}, {'type': 'text', 'x0': 207.0, 'top': 566.0, 'x1': 874.0, 'bottom': 705.0, 'score': 0.9940053820610046}]
2025-08-01 00:15:15,974 DEBUG    36 DiT scaled boxes for page 11 (after scale_factor=3 division): [{'type': 'text', 'score': 0.9947901964187622, 'x0': 67.33333333333333, 'x1': 293.3333333333333, 'top': 314.6666666666667, 'bottom': 393.6666666666667, 'page_number': 11}, {'type': 'text', 'score': 0.9940053820610046, 'x0': 69.0, 'x1': 291.3333333333333, 'top': 188.66666666666666, 'bottom': 235.0, 'page_number': 11}]
2025-08-01 00:15:15,976 DEBUG    36 Image 11 dimensions: (1786, 2526)
2025-08-01 00:15:15,978 DEBUG    36 OCR boxes sample for page 11: [{'x0': 80.66666666666667, 'x1': 291.3333333333333, 'top': 73.66666666666667, 'text': 'Qi Qian, Ji Zhang, et al. 2023b. UReader: Univer-', 'bottom': 86.0, 'page_number': 12}, {'x0': 79.66666666666667, 'x1': 290.6666666666667, 'top': 83.33333333333333, 'text': 'sal OCR-free visually-situated language understand-', 'bottom': 95.66666666666667, 'page_number': 12}]
2025-08-01 00:15:15,985 DEBUG    36 DiT original boxes for page 12 (before scale_factor=3 division): [{'type': 'table', 'x0': 941.3004318237305, 'top': 218.1304770957886, 'x1': 1543.4707055664062, 'bottom': 721.3939986089812, 'score': 0.9992374181747437}, {'type': 'title', 'x0': 207.52495416641236, 'top': 541.2392057626254, 'x1': 569.2280220794678, 'bottom': 579.3321398828642, 'score': 0.9992094039916992}]
2025-08-01 00:15:15,985 DEBUG    36 DiT scaled boxes for page 12 (after scale_factor=3 division): [{'type': 'table', 'score': 0.9992374181747437, 'x0': 313.76681060791014, 'x1': 514.4902351888021, 'top': 72.71015903192954, 'bottom': 240.46466620299373, 'page_number': 12}, {'type': 'title', 'score': 0.9992094039916992, 'x0': 69.17498472213745, 'x1': 189.74267402648925, 'top': 180.4130685875418, 'bottom': 193.11071329428808, 'page_number': 12}]
2025-08-01 00:15:15,990 DEBUG    36 Image 12 dimensions: (1786, 2526)
2025-08-01 00:15:15,992 DEBUG    36 OCR boxes sample for page 12: [{'x0': 79.0, 'x1': 291.3333333333333, 'top': 71.0, 'text': ' In this supplementary material, we provide ad-', 'bottom': 86.0, 'page_number': 13}, {'x0': 318.0, 'x1': 335.0, 'top': 74.66666666666667, 'text': 'No.', 'bottom': 84.33333333333333, 'page_number': 13}]
2025-08-01 00:15:16,011 DEBUG    36 DiT original boxes for page 13 (before scale_factor=3 division): [{'type': 'title', 'x0': 914.0591354370117, 'top': 1894.8889131014794, 'x1': 1369.0, 'bottom': 1937.0, 'score': 0.998823344707489}, {'type': 'figure', 'x0': 1154.9436235046387, 'top': 220.40058522515332, 'x1': 1501.4773860168457, 'bottom': 633.084687979215, 'score': 0.9984968900680542}]
2025-08-01 00:15:16,011 DEBUG    36 DiT scaled boxes for page 13 (after scale_factor=3 division): [{'type': 'title', 'score': 0.998823344707489, 'x0': 304.6863784790039, 'x1': 456.3333333333333, 'top': 631.6296377004932, 'bottom': 645.6666666666666, 'page_number': 13}, {'type': 'figure', 'score': 0.9984968900680542, 'x0': 384.98120783487957, 'x1': 500.4924620056152, 'top': 73.46686174171778, 'bottom': 211.028229326405, 'page_number': 13}]
2025-08-01 00:15:16,013 DEBUG    36 Image 13 dimensions: (1786, 2526)
2025-08-01 00:15:16,015 DEBUG    36 OCR boxes sample for page 13: [{'x0': 350.6666666666667, 'x1': 361.3333333333333, 'top': 102.66666666666667, 'text': 'SENTSAS', 'bottom': 108.66666666666667, 'page_number': 14}, {'x0': 70.0, 'x1': 526.3333333333334, 'top': 370.0, 'text': 'Figure 7: Examples of synthetic training data generated from different source formats. Top: rendered document', 'bottom': 381.6666666666667, 'page_number': 14}]
2025-08-01 00:15:16,021 DEBUG    36 DiT original boxes for page 14 (before scale_factor=3 division): [{'type': 'figure caption', 'x0': 207.0, 'top': 2134.0, 'x1': 1579.0, 'bottom': 2242.0, 'score': 0.9886342287063599}, {'type': 'footer', 'x0': 872.587763595581, 'top': 2379.4207922330897, 'x1': 916.567904586792, 'bottom': 2413.9592200969823, 'score': 0.9792050719261169}]
2025-08-01 00:15:16,021 DEBUG    36 DiT scaled boxes for page 14 (after scale_factor=3 division): [{'type': 'figure caption', 'score': 0.9886342287063599, 'x0': 69.0, 'x1': 526.3333333333334, 'top': 711.3333333333334, 'bottom': 747.3333333333334, 'page_number': 14}, {'type': 'footer', 'score': 0.9792050719261169, 'x0': 290.8625878651937, 'x1': 305.522634862264, 'top': 793.1402640776965, 'bottom': 804.6530733656608, 'page_number': 14}]
2025-08-01 00:15:16,023 DEBUG    36 Image 14 dimensions: (1786, 2526)
2025-08-01 00:15:16,025 DEBUG    36 OCR boxes sample for page 14: [{'x0': 430.0, 'x1': 476.0, 'top': 93.66666666666667, 'text': 'Markdown', 'bottom': 106.0, 'page_number': 15}, {'x0': 142.66666666666666, 'x1': 240.0, 'top': 95.66666666666667, 'text': 'ReadingOrder&Layout', 'bottom': 107.0, 'page_number': 15}]
2025-08-01 00:15:16,032 DEBUG    36 DiT original boxes for page 15 (before scale_factor=3 division): [{'type': 'title', 'x0': 720.0, 'top': 302.5769829788005, 'x1': 1068.0, 'bottom': 342.0, 'score': 0.9877061247825623}, {'type': 'title', 'x0': 340.0, 'top': 302.08975106398685, 'x1': 521.0, 'bottom': 342.0, 'score': 0.9731138348579407}]
2025-08-01 00:15:16,032 DEBUG    36 DiT scaled boxes for page 15 (after scale_factor=3 division): [{'type': 'title', 'score': 0.9877061247825623, 'x0': 240.0, 'x1': 356.0, 'top': 100.85899432626684, 'bottom': 114.0, 'page_number': 15}, {'type': 'title', 'score': 0.9731138348579407, 'x0': 113.33333333333333, 'x1': 173.66666666666666, 'top': 100.69658368799561, 'bottom': 114.0, 'page_number': 15}]
2025-08-01 00:15:16,034 DEBUG    36 Image 15 dimensions: (1786, 2526)
2025-08-01 00:15:16,036 DEBUG    36 OCR boxes sample for page 15: [{'x0': 113.33333333333333, 'x1': 173.66666666666666, 'top': 101.66666666666667, 'text': 'Input Image', 'bottom': 114.0, 'page_number': 16}, {'x0': 240.0, 'x1': 356.0, 'top': 101.66666666666667, 'text': 'Reading Order & Layout', 'bottom': 114.0, 'page_number': 16}]
2025-08-01 00:15:16,053 DEBUG    36 DiT original boxes for page 16 (before scale_factor=3 division): [{'type': 'footer', 'x0': 871.3135189819336, 'top': 2376.748974220822, 'x1': 917.0371192932129, 'bottom': 2416.3453171626325, 'score': 0.9828945398330688}, {'type': 'table', 'x0': 209.4599112701416, 'top': 257.5358566162757, 'x1': 1580.6364346313476, 'bottom': 2102.0, 'score': 0.8937831521034241}]
2025-08-01 00:15:16,053 DEBUG    36 DiT scaled boxes for page 16 (after scale_factor=3 division): [{'type': 'footer', 'score': 0.9828945398330688, 'x0': 290.43783966064456, 'x1': 305.6790397644043, 'top': 792.2496580736074, 'bottom': 805.4484390542108, 'page_number': 16}, {'type': 'table', 'score': 0.8937831521034241, 'x0': 69.81997042338052, 'x1': 526.8788115437825, 'top': 85.84528553875856, 'bottom': 700.6666666666666, 'page_number': 16}]
2025-08-01 00:15:16,055 DEBUG    36 Image 16 dimensions: (1786, 2526)
2025-08-01 00:15:16,057 DEBUG    36 OCR boxes sample for page 16: [{'x0': 77.33333333333333, 'x1': 158.66666666666666, 'top': 109.66666666666667, 'text': ' Inline formula image', 'bottom': 121.66666666666667, 'page_number': 17}, {'x0': 172.66666666666666, 'x1': 522.6666666666666, 'top': 109.66666666666667, 'text': 'is normalized by ≥=1 ≥k=1 Amlqk = 1. Here, we use normalized coordinates pPa. E [0, 1]2 for', 'bottom': 124.66666666666667, 'page_number': 17}]
2025-08-01 00:15:16,063 DEBUG    36 DiT original boxes for page 17 (before scale_factor=3 division): [{'type': 'table', 'x0': 376.9895236968994, 'top': 226.11234597122635, 'x1': 1404.9450616455078, 'bottom': 1020.9257224814012, 'score': 0.9996875524520874}, {'type': 'figure caption', 'x0': 199.04745986938477, 'top': 2198.424206316313, 'x1': 1579.0, 'bottom': 2305.0, 'score': 0.999538779258728}]
2025-08-01 00:15:16,063 DEBUG    36 DiT scaled boxes for page 17 (after scale_factor=3 division): [{'type': 'table', 'score': 0.9996875524520874, 'x0': 125.66317456563314, 'x1': 468.3150205485026, 'top': 75.37078199040879, 'bottom': 340.30857416046706, 'page_number': 17}, {'type': 'figure caption', 'score': 0.999538779258728, 'x0': 66.34915328979493, 'x1': 526.3333333333334, 'top': 732.8080687721043, 'bottom': 768.3333333333334, 'page_number': 17}]
2025-08-01 00:15:16,065 DEBUG    36 Image 17 dimensions: (1786, 2526)
2025-08-01 00:15:16,067 DEBUG    36 OCR boxes sample for page 17: [{'x0': 196.66666666666666, 'x1': 206.33333333333334, 'top': 79.0, 'text': 'BG', 'bottom': 86.0, 'page_number': 18}, {'x0': 216.0, 'x1': 224.0, 'top': 79.0, 'text': 'DE', 'bottom': 86.0, 'page_number': 18}]
2025-08-01 00:15:16,100 INFO     36 set_progress(5f24b84e6e2911f0983202420ae90a06), progress: 0.63, progress_msg: 00:15:16 Page(1~19): Layout analysis (32.99s)
2025-08-01 00:15:16,121 DEBUG    36 [CustomTT] processing 15 tables via REST API
2025-08-01 00:15:18,279 INFO     36 task_executor_332ded9aa4f2_0 reported heartbeat: {"name": "task_executor_332ded9aa4f2_0", "now": "2025-08-01T00:15:18.275+08:00", "boot_at": "2025-08-01T00:11:47.688+08:00", "pending": 2, "lag": 0, "done": 1, "failed": 0, "current": {"5e7a352c6e2911f0bdae02420ae90a06": {"id": "5e7a352c6e2911f0bdae02420ae90a06", "doc_id": "52be32ce6e2911f0863602420ae90a06", "from_page": 0, "to_page": 4, "retry_count": 0, "kb_id": "1dde70d26e2911f08f4502420ae90a06", "parser_id": "paper", "parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Bank Holiday Swap Program Guide.pdf", "type": "pdf", "location": "Bank Holiday Swap Program Guide.pdf", "size": 482309, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": *************, "task_type": ""}, "5f24b84e6e2911f0983202420ae90a06": {"id": "5f24b84e6e2911f0983202420ae90a06", "doc_id": "4a88ed1a6e2911f0a36102420ae90a06", "from_page": 0, "to_page": 18, "retry_count": 0, "kb_id": "1dde70d26e2911f08f4502420ae90a06", "parser_id": "paper", "parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "2505.14059v1.pdf", "type": "pdf", "location": "2505.14059v1.pdf", "size": 20037471, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1753978441755, "task_type": ""}}}
2025-08-01 00:15:27,616 DEBUG    36 [CustomTT] crop #0 output keys: ['table_objects', 'html', 'csv', 'score', 'qc_results']
2025-08-01 00:15:27,617 DEBUG    36 [CustomTT] crop #0 converted 43 table_objects to 43 components
2025-08-01 00:15:27,621 DEBUG    36 [CustomTT] crop #0 → 43 components
2025-08-01 00:15:35,593 DEBUG    36 [CustomTT] crop #1 output keys: ['table_objects', 'html', 'csv', 'score', 'qc_results']
2025-08-01 00:15:35,594 DEBUG    36 [CustomTT] crop #1 converted 14 table_objects to 14 components
2025-08-01 00:15:35,600 DEBUG    36 [CustomTT] crop #1 → 14 components
2025-08-01 00:15:45,878 DEBUG    36 [CustomTT] crop #2 output keys: ['table_objects', 'html', 'csv', 'score', 'qc_results']
2025-08-01 00:15:45,878 DEBUG    36 [CustomTT] crop #2 converted 40 table_objects to 40 components
2025-08-01 00:15:45,883 DEBUG    36 [CustomTT] crop #2 → 40 components
2025-08-01 00:15:48,288 INFO     36 task_executor_332ded9aa4f2_0 reported heartbeat: {"name": "task_executor_332ded9aa4f2_0", "now": "2025-08-01T00:15:48.285+08:00", "boot_at": "2025-08-01T00:11:47.688+08:00", "pending": 2, "lag": 0, "done": 1, "failed": 0, "current": {"5e7a352c6e2911f0bdae02420ae90a06": {"id": "5e7a352c6e2911f0bdae02420ae90a06", "doc_id": "52be32ce6e2911f0863602420ae90a06", "from_page": 0, "to_page": 4, "retry_count": 0, "kb_id": "1dde70d26e2911f08f4502420ae90a06", "parser_id": "paper", "parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Bank Holiday Swap Program Guide.pdf", "type": "pdf", "location": "Bank Holiday Swap Program Guide.pdf", "size": 482309, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": *************, "task_type": ""}, "5f24b84e6e2911f0983202420ae90a06": {"id": "5f24b84e6e2911f0983202420ae90a06", "doc_id": "4a88ed1a6e2911f0a36102420ae90a06", "from_page": 0, "to_page": 18, "retry_count": 0, "kb_id": "1dde70d26e2911f08f4502420ae90a06", "parser_id": "paper", "parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "2505.14059v1.pdf", "type": "pdf", "location": "2505.14059v1.pdf", "size": 20037471, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1753978441755, "task_type": ""}}}
2025-08-01 00:15:53,221 DEBUG    36 [CustomTT] crop #3 output keys: ['table_objects', 'html', 'csv', 'score', 'qc_results']
2025-08-01 00:15:53,222 DEBUG    36 [CustomTT] crop #3 converted 11 table_objects to 11 components
2025-08-01 00:15:53,225 DEBUG    36 [CustomTT] crop #3 → 11 components
2025-08-01 00:16:05,233 DEBUG    36 [CustomTT] crop #4 output keys: ['table_objects', 'html', 'csv', 'score', 'qc_results']
2025-08-01 00:16:05,234 DEBUG    36 [CustomTT] crop #4 converted 21 table_objects to 21 components
2025-08-01 00:16:05,236 DEBUG    36 [CustomTT] crop #4 → 21 components
2025-08-01 00:16:12,087 DEBUG    36 [CustomTT] crop #5 output keys: ['table_objects', 'html', 'csv', 'score', 'qc_results']
2025-08-01 00:16:12,088 DEBUG    36 [CustomTT] crop #5 converted 11 table_objects to 11 components
2025-08-01 00:16:12,091 DEBUG    36 [CustomTT] crop #5 → 11 components
2025-08-01 00:16:18,295 INFO     36 task_executor_332ded9aa4f2_0 reported heartbeat: {"name": "task_executor_332ded9aa4f2_0", "now": "2025-08-01T00:16:18.292+08:00", "boot_at": "2025-08-01T00:11:47.688+08:00", "pending": 2, "lag": 0, "done": 1, "failed": 0, "current": {"5e7a352c6e2911f0bdae02420ae90a06": {"id": "5e7a352c6e2911f0bdae02420ae90a06", "doc_id": "52be32ce6e2911f0863602420ae90a06", "from_page": 0, "to_page": 4, "retry_count": 0, "kb_id": "1dde70d26e2911f08f4502420ae90a06", "parser_id": "paper", "parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Bank Holiday Swap Program Guide.pdf", "type": "pdf", "location": "Bank Holiday Swap Program Guide.pdf", "size": 482309, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": *************, "task_type": ""}, "5f24b84e6e2911f0983202420ae90a06": {"id": "5f24b84e6e2911f0983202420ae90a06", "doc_id": "4a88ed1a6e2911f0a36102420ae90a06", "from_page": 0, "to_page": 18, "retry_count": 0, "kb_id": "1dde70d26e2911f08f4502420ae90a06", "parser_id": "paper", "parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "2505.14059v1.pdf", "type": "pdf", "location": "2505.14059v1.pdf", "size": 20037471, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "auto_keywords": 0, "auto_questions": 0, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1753978441755, "task_type": ""}}}
2025-08-01 00:16:19,819 DEBUG    36 [CustomTT] crop #6 output keys: ['table_objects', 'html', 'csv', 'score', 'qc_results']
2025-08-01 00:16:19,819 DEBUG    36 [CustomTT] crop #6 converted 15 table_objects to 15 components
2025-08-01 00:16:19,821 DEBUG    36 [CustomTT] crop #6 → 15 components
2025-08-01 00:16:27,902 DEBUG    36 [CustomTT] crop #7 output keys: ['table_objects', 'html', 'csv', 'score', 'qc_results']
2025-08-01 00:16:27,903 DEBUG    36 [CustomTT] crop #7 converted 8 table_objects to 8 components
2025-08-01 00:16:27,907 DEBUG    36 [CustomTT] crop #7 → 8 components
